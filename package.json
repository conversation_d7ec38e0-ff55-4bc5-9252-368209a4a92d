{"name": "pi-pf-portal-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:comprehensive": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:comprehensive-core": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:comprehensive-extended": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:working-coverage-clean": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/copy/copy.component.working.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:comprehensive-setup": "ng test --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:comprehensive-with-edit": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:working-components": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/edit/edit.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:simple-coverage": "ng test --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:working-only": "ng test --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:services-only": "ng test --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:working-coverage": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:working-no-copy": "ng test --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:rules-core-only": "ng test --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:all-working-stable": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:stable-with-copy": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:copy-only": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:create-only": "ng test --include=\"src/app/rules/create/create.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:edit-only": "ng test --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:view-only": "ng test --include=\"src/app/rules/view/view.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:rule-history-only": "ng test --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:core-with-history": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:stable-components": "ng test --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:core-with-copy": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:copy-working": "ng test --include=\"src/app/rules/copy/copy.component.working.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:stable-final": "ng test --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:final-coverage": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:show-coverage": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:stable-coverage": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:minimal-coverage": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:actual-coverage": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/_services/authentication.services.spec.ts\" --include=\"src/app/_services/ecm-authentication.service.spec.ts\" --include=\"src/app/_services/toast.service.spec.ts\" --include=\"src/app/_services/utilities.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:all-rules-components": "ng test --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/shared/breadcrumbs-nav/breadcrumbs-nav.component.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:core-components-85plus": "ng test --include=\"src/app/rules/edit/edit.component.working.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:edit-component-85plus": "ng test --include=\"src/app/rules/edit/edit.component.85plus.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:edit-only-final": "ng test --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:all-working": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:all-working-with-edit": "ng test --include=\"src/app/rules/copy/copy.component.spec.ts\" --include=\"src/app/rules/view/view.component.spec.ts\" --include=\"src/app/rules/create/create.component.spec.ts\" --include=\"src/app/rules/edit/edit.component.final50.spec.ts\" --include=\"src/app/rules/impact-report/impact-report.component.spec.ts\" --include=\"src/app/rules/rule-history/rule-history.component.spec.ts\" --include=\"src/app/rules/rules.component.spec.ts\" --include=\"src/app/rules/dashboard/dashboard.component.spec.ts\" --include=\"src/app/rules/frequently-used-criteria/frequently-used-criteria.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/setup-type.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-details/type-details.component.spec.ts\" --include=\"src/app/rules/setup-rule-type/type-outcome/type-outcome.component.spec.ts\" --include=\"src/app/rules/create-new-criteria/create-new-criteria.component.spec.ts\" --include=\"src/app/rules/rules-comprehensive-coverage.spec.ts\" --include=\"src/app/rules/rules-core-logic.spec.ts\" --include=\"src/app/rules/_services/Rules-QB-Constants.spec.ts\" --include=\"src/app/rules/_services/rules-api.service.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:edit": "ng test --include=\"src/app/rules/edit/edit.component.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage", "test:auth": "ng test --include=\"src/app/_services/authentication.services.spec.ts\" --watch=false --browsers=ChromeHeadless --code-coverage"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/router": "^17.3.0", "@ng-idle/core": "^16.0.0", "@ng-idle/keepalive": "^16.0.0", "bn-ng-idle": "^2.0.4", "bootstrap": "^4.6.2", "crypto-js": "^4.2.0", "jose": "^5.9.6", "jquery": "^3.7.1", "marketplace-accordion": "./src/packages/accordion/marketplace-accordion-0.0.1.tgz", "marketplace-alert": "./src/packages/alert/marketplace-alert-0.0.1.tgz", "marketplace-breadcrumb": "./src/packages/breadcrumb/marketplace-breadcrumb-0.0.1.tgz", "marketplace-button": "./src/packages/button/marketplace-button-0.0.1.tgz", "marketplace-cards": "./src/packages/cards/marketplace-cards-0.0.1.tgz", "marketplace-charts": "./src/packages/charts/marketplace-charts-0.0.1.tgz", "marketplace-checkbox": "./src/packages/checkbox/marketplace-checkbox-0.0.1.tgz", "marketplace-date-picker": "./src/packages/date-picker/marketplace-date-picker-0.0.1.tgz", "marketplace-file-parser": "./src/packages/file-parser/marketplace-file-parser-0.0.1.tgz", "marketplace-file-upload": "./src/packages/file-upload/marketplace-file-upload-0.0.1.tgz", "marketplace-footer": "./src/packages/footer/marketplace-footer-0.0.1.tgz", "marketplace-form": "./src/packages/form/marketplace-form-0.0.1.tgz", "marketplace-form-repeater": "./src/packages/form-repeater/marketplace-form-repeater-0.0.1.tgz", "marketplace-header": "./src/packages/header/marketplace-header-0.0.1.tgz", "marketplace-input": "./src/packages/input/marketplace-input-0.0.1.tgz", "marketplace-jwt-verifier": "./src/packages/jwt-verifier-service/marketplace-jwt-verifier-0.0.1.tgz", "marketplace-left-menu": "./src/packages/left-menu/marketplace-left-menu-0.0.1.tgz", "marketplace-list-picker": "./src/packages/list-picker/marketplace-list-picker-0.0.1.tgz", "marketplace-modal": "./src/packages/modal/marketplace-modal-0.0.1.tgz", "marketplace-notification": "./src/packages/notification/marketplace-notification-0.0.1.tgz", "marketplace-pagination": "./src/packages/pagination/marketplace-pagination-0.0.1.tgz", "marketplace-popup": "./src/packages/popup/marketplace-popup-0.0.1.tgz", "marketplace-progressbar": "./src/packages/progress-bar/marketplace-progressbar-0.0.1.tgz", "marketplace-query-builder": "./src/packages/query-builder/marketplace-query-builder-0.0.1.tgz", "marketplace-quick-navs": "./src/packages/quick-navs/marketplace-quick-navs-0.0.1.tgz", "marketplace-radio-button": "./src/packages/radio-button/marketplace-radio-button-0.0.1.tgz", "marketplace-segmented-control": "./src/packages/segmented-control/marketplace-segmented-control-0.0.1.tgz", "marketplace-select": "./src/packages/select/marketplace-select-0.0.1.tgz", "marketplace-slide-panel": "./src/packages/slide-panel/marketplace-slide-panel-0.0.1.tgz", "marketplace-stepper": "./src/packages/stepper/marketplace-stepper-0.0.1.tgz", "marketplace-switch": "./src/packages/switch/marketplace-switch-0.0.1.tgz", "marketplace-table": "./src/packages/table/marketplace-table-0.0.1.tgz", "marketplace-tabs": "./src/packages/tabs/marketplace-tabs-0.0.1.tgz", "marketplace-target-cards": "./src/packages/target-cards/marketplace-target-cards-0.0.1.tgz", "marketplace-textarea": "./src/packages/text-area/marketplace-textarea-0.0.1.tgz", "marketplace-time-picker": "./src/packages/time-picker/marketplace-time-picker-0.0.1.tgz", "marketplace-timeline": "./src/packages/timeline/marketplace-timeline-0.0.1.tgz", "marketplace-toggle-button": "./src/packages/toggle-button/marketplace-toggle-button-0.0.1.tgz", "marketplace-view-list": "./src/packages/view-list/marketplace-view-list-0.0.1.tgz", "ngx-cookie-service": "^17.1.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.0", "@angular/cli": "^17.3.0", "@angular/compiler-cli": "^17.3.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}