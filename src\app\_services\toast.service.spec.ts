import { TestBed } from '@angular/core/testing';
import { ToastService } from './toast.service';
import { NOTIFICATION_CONSTANT } from '../_constants/notification_constants';

describe('ToastService', () => {
  let service: ToastService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ToastService);
    jasmine.clock().install();
  });

  afterEach(() => {
    jasmine.clock().uninstall();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Service Initialization', () => {
    it('should initialize with default values', () => {
      expect(service.notification).toEqual({});
      expect(service.showAlert).toBe(false);
    });
  });

  describe('setNotification', () => {
    it('should set notification and show alert', () => {
      const mockNotification = {
        notificationOpen: true,
        notificationHeader: 'Test Header',
        notificationBody: 'Test Body',
        notificationType: 'success',
        notificationPosition: 'top-right',
        notificationDuration: 3000
      };

      service.setNotification(mockNotification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(mockNotification);
    });

    it('should clear notification after timeout', () => {
      const mockNotification = {
        notificationDuration: 1000
      };

      service.setNotification(mockNotification);
      expect(service.showAlert).toBe(true);

      jasmine.clock().tick(1001);
      expect(service.showAlert).toBe(false);
      expect(service.notification).toEqual({});
    });
  });

  describe('clearNotification', () => {
    it('should clear notification and hide alert', () => {
      service.notification = { test: 'data' };
      service.showAlert = true;

      service.clearNotification();

      expect(service.notification).toEqual({});
      expect(service.showAlert).toBe(false);
    });

    it('should work when notification is already empty', () => {
      service.notification = {};
      service.showAlert = false;

      service.clearNotification();

      expect(service.notification).toEqual({});
      expect(service.showAlert).toBe(false);
    });
  });

  describe('setSuccessNotification', () => {
    it('should set success notification with required parameters only', () => {
      const notification = {
        notificationBody: 'Success message'
      };

      service.setSuccessNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER,
        notificationBody: 'Success message',
        notificationType: NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION,
        notificationPosition: NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
        notificationDuration: NOTIFICATION_CONSTANT.NOTIFICATION_DURATION
      });
    });

    it('should set success notification with all parameters', () => {
      const notification = {
        notificationBody: 'Success message',
        notificationHeader: 'Custom Success',
        notificationDuration: 5000,
        notificationPosition: 'bottom-left'
      };

      service.setSuccessNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: 'Custom Success',
        notificationBody: 'Success message',
        notificationType: NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION,
        notificationPosition: 'bottom-left',
        notificationDuration: 5000
      });
    });

    it('should clear notification after timeout', () => {
      const notification = {
        notificationBody: 'Success message',
        notificationDuration: 1000
      };

      service.setSuccessNotification(notification);
      expect(service.showAlert).toBe(true);

      jasmine.clock().tick(1001);
      expect(service.showAlert).toBe(false);
    });
  });

  describe('setErrorNotification', () => {
    it('should set error notification with required parameters only', () => {
      const notification = {
        notificationBody: 'Error message'
      };

      service.setErrorNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: NOTIFICATION_CONSTANT.ERROR_NOTIFICATION_HEADER,
        notificationBody: 'Error message',
        notificationType: NOTIFICATION_CONSTANT.ERROR_NOTIFICATION,
        notificationPosition: NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
        notificationDuration: NOTIFICATION_CONSTANT.NOTIFICATION_DURATION
      });
    });

    it('should set error notification with all parameters', () => {
      const notification = {
        notificationBody: 'Error message',
        notificationHeader: 'Custom Error',
        notificationDuration: 4000,
        notificationPosition: 'top-left'
      };

      service.setErrorNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: 'Custom Error',
        notificationBody: 'Error message',
        notificationType: NOTIFICATION_CONSTANT.ERROR_NOTIFICATION,
        notificationPosition: 'top-left',
        notificationDuration: 4000
      });
    });
  });

  describe('setWarningNotification', () => {
    it('should set warning notification with required parameters only', () => {
      const notification = {
        notificationBody: 'Warning message'
      };

      service.setWarningNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: NOTIFICATION_CONSTANT.WARNING_NOTIFICATION_HEADER,
        notificationBody: 'Warning message',
        notificationType: NOTIFICATION_CONSTANT.WARNING_NOTIFICATION,
        notificationPosition: NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
        notificationDuration: NOTIFICATION_CONSTANT.NOTIFICATION_DURATION
      });
    });

    it('should set warning notification with all parameters', () => {
      const notification = {
        notificationBody: 'Warning message',
        notificationHeader: 'Custom Warning',
        notificationDuration: 6000,
        notificationPosition: 'bottom-right'
      };

      service.setWarningNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: 'Custom Warning',
        notificationBody: 'Warning message',
        notificationType: NOTIFICATION_CONSTANT.WARNING_NOTIFICATION,
        notificationPosition: 'bottom-right',
        notificationDuration: 6000
      });
    });
  });

  describe('setInfoNotification', () => {
    it('should set info notification with required parameters only', () => {
      const notification = {
        notificationBody: 'Info message'
      };

      service.setInfoNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: NOTIFICATION_CONSTANT.INFO_NOTIFICATION_HEADER,
        notificationBody: 'Info message',
        notificationType: NOTIFICATION_CONSTANT.INFO_NOTIFICATION,
        notificationPosition: NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
        notificationDuration: NOTIFICATION_CONSTANT.NOTIFICATION_DURATION
      });
    });

    it('should set info notification with all parameters', () => {
      const notification = {
        notificationBody: 'Info message',
        notificationHeader: 'Custom Info',
        notificationDuration: 2000,
        notificationPosition: 'center'
      };

      service.setInfoNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: 'Custom Info',
        notificationBody: 'Info message',
        notificationType: NOTIFICATION_CONSTANT.INFO_NOTIFICATION,
        notificationPosition: 'center',
        notificationDuration: 2000
      });
    });
  });

  describe('setDownloadNotification', () => {
    it('should set download notification with required parameters only', () => {
      const notification = {
        notificationBody: 'Download complete'
      };

      service.setDownloadNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER,
        notificationBody: 'Download complete',
        notificationType: NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION,
        notificationPosition: NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
        notificationDuration: NOTIFICATION_CONSTANT.DOWNLOAD_NOTIFICATION_DURATION
      });
    });

    it('should set download notification with all parameters', () => {
      const notification = {
        notificationBody: 'Download complete',
        notificationHeader: 'Download Success',
        notificationDuration: 8000,
        notificationPosition: 'top-center'
      };

      service.setDownloadNotification(notification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual({
        notificationOpen: true,
        notificationHeader: 'Download Success',
        notificationBody: 'Download complete',
        notificationType: NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION,
        notificationPosition: 'top-center',
        notificationDuration: 8000
      });
    });

    it('should clear notification after fixed timeout of 10ms', () => {
      const notification = {
        notificationBody: 'Download complete'
      };

      service.setDownloadNotification(notification);
      expect(service.showAlert).toBe(true);

      jasmine.clock().tick(11);
      expect(service.showAlert).toBe(false);
    });
  });

  describe('Edge Cases and Integration', () => {
    it('should handle multiple consecutive notifications', () => {
      service.setSuccessNotification({ notificationBody: 'First' });
      expect(service.notification.notificationBody).toBe('First');

      service.setErrorNotification({ notificationBody: 'Second' });
      expect(service.notification.notificationBody).toBe('Second');
      expect(service.notification.notificationType).toBe(NOTIFICATION_CONSTANT.ERROR_NOTIFICATION);
    });

    it('should handle empty notification body', () => {
      const notification = {
        notificationBody: ''
      };

      service.setSuccessNotification(notification);
      expect(service.notification.notificationBody).toBe('');
      expect(service.showAlert).toBe(true);
    });

    it('should handle zero duration and use default when not provided', () => {
      // Test with zero duration - service uses default when 0 is provided
      const notification = {
        notificationBody: 'Test',
        notificationDuration: 0
      };

      service.setSuccessNotification(notification);
      // The service logic treats 0 as falsy and uses default
      expect(service.notification.notificationDuration).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_DURATION);

      // Test without duration - should use default
      const notificationNoDelay = {
        notificationBody: 'Test No Duration'
      };

      service.setSuccessNotification(notificationNoDelay);
      expect(service.notification.notificationDuration).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_DURATION);
    });

    it('should maintain state consistency across different notification types', () => {
      // Test success
      service.setSuccessNotification({ notificationBody: 'Success' });
      expect(service.showAlert).toBe(true);
      expect(service.notification.notificationType).toBe(NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION);

      // Test error
      service.setErrorNotification({ notificationBody: 'Error' });
      expect(service.showAlert).toBe(true);
      expect(service.notification.notificationType).toBe(NOTIFICATION_CONSTANT.ERROR_NOTIFICATION);

      // Test warning
      service.setWarningNotification({ notificationBody: 'Warning' });
      expect(service.showAlert).toBe(true);
      expect(service.notification.notificationType).toBe(NOTIFICATION_CONSTANT.WARNING_NOTIFICATION);

      // Test info
      service.setInfoNotification({ notificationBody: 'Info' });
      expect(service.showAlert).toBe(true);
      expect(service.notification.notificationType).toBe(NOTIFICATION_CONSTANT.INFO_NOTIFICATION);
    });

    it('should handle manual clear after automatic timeout', () => {
      service.setSuccessNotification({ 
        notificationBody: 'Test',
        notificationDuration: 1000
      });

      service.clearNotification();
      expect(service.showAlert).toBe(false);

      // Even after timeout, should remain cleared
      jasmine.clock().tick(1001);
      expect(service.showAlert).toBe(false);
    });

    it('should handle notification with all falsy optional parameters', () => {
      const notification = {
        notificationBody: 'Test Body',
        notificationHeader: '',
        notificationDuration: 0,
        notificationPosition: ''
      };

      service.setSuccessNotification(notification);

      expect(service.notification.notificationHeader).toBe(NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER);
      expect(service.notification.notificationDuration).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_DURATION);
      expect(service.notification.notificationPosition).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_POSITION);
    });

    it('should handle notification with null optional parameters', () => {
      const notification = {
        notificationBody: 'Test Body',
        notificationHeader: null,
        notificationDuration: null,
        notificationPosition: null
      };

      service.setErrorNotification(notification);

      expect(service.notification.notificationHeader).toBe(NOTIFICATION_CONSTANT.ERROR_NOTIFICATION_HEADER);
      expect(service.notification.notificationDuration).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_DURATION);
      expect(service.notification.notificationPosition).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_POSITION);
    });

    it('should handle notification with undefined optional parameters', () => {
      const notification = {
        notificationBody: 'Test Body',
        notificationHeader: undefined,
        notificationDuration: undefined,
        notificationPosition: undefined
      };

      service.setWarningNotification(notification);

      expect(service.notification.notificationHeader).toBe(NOTIFICATION_CONSTANT.WARNING_NOTIFICATION_HEADER);
      expect(service.notification.notificationDuration).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_DURATION);
      expect(service.notification.notificationPosition).toBe(NOTIFICATION_CONSTANT.NOTIFICATION_POSITION);
    });

    it('should handle setNotification with timeout clearing', () => {
      const mockNotification = {
        notificationOpen: true,
        notificationHeader: 'Test',
        notificationBody: 'Test Body',
        notificationType: 'info',
        notificationPosition: 'top-right',
        notificationDuration: 500
      };

      service.setNotification(mockNotification);
      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(mockNotification);

      // Fast forward time to trigger timeout
      jasmine.clock().tick(501);
      expect(service.showAlert).toBe(false);
      expect(service.notification).toEqual({});
    });

    it('should handle rapid successive notification calls', () => {
      service.setSuccessNotification({ notificationBody: 'First' });
      service.setErrorNotification({ notificationBody: 'Second' });
      service.setWarningNotification({ notificationBody: 'Third' });
      service.setInfoNotification({ notificationBody: 'Fourth' });
      service.setDownloadNotification({ notificationBody: 'Fifth' });

      expect(service.notification.notificationBody).toBe('Fifth');
      expect(service.notification.notificationType).toBe(NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION);
      expect(service.showAlert).toBe(true);
    });

    it('should handle clearNotification when already cleared', () => {
      service.clearNotification();
      expect(service.notification).toEqual({});
      expect(service.showAlert).toBe(false);

      // Clear again - should not cause issues
      service.clearNotification();
      expect(service.notification).toEqual({});
      expect(service.showAlert).toBe(false);
    });

    it('should handle setDownloadNotification timeout behavior', () => {
      const notification = {
        notificationBody: 'Download test',
        notificationDuration: 5000 // This should be ignored for download notifications
      };

      service.setDownloadNotification(notification);
      expect(service.showAlert).toBe(true);

      // Download notifications have a fixed 10ms timeout
      jasmine.clock().tick(11);
      expect(service.showAlert).toBe(false);
    });

    it('should handle notification state persistence', () => {
      const notification = {
        notificationBody: 'Persistent test',
        notificationHeader: 'Custom Header',
        notificationDuration: 2000,
        notificationPosition: 'bottom-center'
      };

      service.setInfoNotification(notification);

      // Verify all properties are set correctly
      expect(service.notification.notificationOpen).toBe(true);
      expect(service.notification.notificationHeader).toBe('Custom Header');
      expect(service.notification.notificationBody).toBe('Persistent test');
      expect(service.notification.notificationType).toBe(NOTIFICATION_CONSTANT.INFO_NOTIFICATION);
      expect(service.notification.notificationPosition).toBe('bottom-center');
      expect(service.notification.notificationDuration).toBe(2000);
      expect(service.showAlert).toBe(true);
    });
  });

  // MASSIVE COVERAGE BOOST - 85%+ TARGET TESTS
  describe('85%+ Coverage Boost - Comprehensive Toast Testing', () => {
    it('should handle setNotification method with all notification types', () => {
      const successNotification = {
        notificationOpen: true,
        notificationHeader: 'Success',
        notificationBody: 'Operation completed successfully',
        notificationType: 'success',
        notificationPosition: 'top-right',
        notificationDuration: 3000
      };

      service.setNotification(successNotification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(successNotification);
    });

    it('should handle setNotification method with error notifications', () => {
      const errorNotification = {
        notificationOpen: true,
        notificationHeader: 'Error',
        notificationBody: 'Operation failed',
        notificationType: 'error',
        notificationPosition: 'top-center',
        notificationDuration: 5000
      };

      service.setNotification(errorNotification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(errorNotification);
    });

    it('should handle setNotification method with warning notifications', () => {
      const warningNotification = {
        notificationOpen: true,
        notificationHeader: 'Warning',
        notificationBody: 'Please check your input',
        notificationType: 'warning',
        notificationPosition: 'bottom-right',
        notificationDuration: 4000
      };

      service.setNotification(warningNotification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(warningNotification);
    });

    it('should handle setNotification method with info notifications', () => {
      const infoNotification = {
        notificationOpen: true,
        notificationHeader: 'Information',
        notificationBody: 'Here is some useful information',
        notificationType: 'info',
        notificationPosition: 'bottom-left',
        notificationDuration: 2000
      };

      service.setNotification(infoNotification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(infoNotification);
    });

    it('should handle clearNotification method comprehensively', () => {
      // First set a notification
      const testNotification = {
        notificationOpen: true,
        notificationHeader: 'Test',
        notificationBody: 'Test message',
        notificationType: 'success',
        notificationPosition: 'top-right',
        notificationDuration: 3000
      };

      service.setNotification(testNotification);
      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(testNotification);

      // Then clear it
      service.clearNotification();

      expect(service.showAlert).toBe(false);
      expect(service.notification).toEqual({});
    });

    it('should handle multiple consecutive notifications', () => {
      const notification1 = {
        notificationOpen: true,
        notificationHeader: 'First',
        notificationBody: 'First message',
        notificationType: 'success',
        notificationPosition: 'top-right',
        notificationDuration: 1000
      };

      const notification2 = {
        notificationOpen: true,
        notificationHeader: 'Second',
        notificationBody: 'Second message',
        notificationType: 'error',
        notificationPosition: 'top-left',
        notificationDuration: 2000
      };

      // Set first notification
      service.setNotification(notification1);
      expect(service.notification).toEqual(notification1);

      // Set second notification (should replace first)
      service.setNotification(notification2);
      expect(service.notification).toEqual(notification2);
      expect(service.showAlert).toBe(true);
    });

    it('should handle notifications with different positions', () => {
      const positions = ['top-left', 'top-center', 'top-right', 'bottom-left', 'bottom-center', 'bottom-right'];

      positions.forEach((position, index) => {
        const notification = {
          notificationOpen: true,
          notificationHeader: `Test ${index + 1}`,
          notificationBody: `Message for position ${position}`,
          notificationType: 'info',
          notificationPosition: position,
          notificationDuration: 3000
        };

        service.setNotification(notification);

        expect(service.notification.notificationPosition).toBe(position);
        expect(service.showAlert).toBe(true);
      });
    });

    it('should handle notifications with different durations', () => {
      const durations = [1000, 2000, 3000, 4000, 5000, 10000];

      durations.forEach((duration, index) => {
        const notification = {
          notificationOpen: true,
          notificationHeader: `Duration Test ${index + 1}`,
          notificationBody: `Message with ${duration}ms duration`,
          notificationType: 'success',
          notificationPosition: 'top-right',
          notificationDuration: duration
        };

        service.setNotification(notification);

        expect(service.notification.notificationDuration).toBe(duration);
        expect(service.showAlert).toBe(true);
      });
    });

    it('should handle notifications with empty or minimal data', () => {
      const minimalNotification = {
        notificationOpen: true,
        notificationHeader: '',
        notificationBody: '',
        notificationType: 'info',
        notificationPosition: 'top-right',
        notificationDuration: 3000
      };

      service.setNotification(minimalNotification);

      expect(service.notification.notificationHeader).toBe('');
      expect(service.notification.notificationBody).toBe('');
      expect(service.showAlert).toBe(true);
    });

    it('should handle notifications with long messages', () => {
      const longMessage = 'This is a very long notification message that contains a lot of text to test how the service handles lengthy content. '.repeat(5);

      const longNotification = {
        notificationOpen: true,
        notificationHeader: 'Long Message Test',
        notificationBody: longMessage,
        notificationType: 'warning',
        notificationPosition: 'top-center',
        notificationDuration: 8000
      };

      service.setNotification(longNotification);

      expect(service.notification.notificationBody).toBe(longMessage);
      expect(service.notification.notificationBody.length).toBeGreaterThan(500);
      expect(service.showAlert).toBe(true);
    });

    it('should handle comprehensive notification lifecycle', () => {
      // Test initial state
      expect(service.showAlert).toBe(false);
      expect(service.notification).toEqual({});

      // Test setting notification
      const testNotification = {
        notificationOpen: true,
        notificationHeader: 'Lifecycle Test',
        notificationBody: 'Testing complete notification lifecycle',
        notificationType: 'success',
        notificationPosition: 'bottom-right',
        notificationDuration: 3000
      };

      service.setNotification(testNotification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(testNotification);

      // Test clearing notification
      service.clearNotification();

      expect(service.showAlert).toBe(false);
      expect(service.notification).toEqual({});

      // Test setting another notification after clearing
      const secondNotification = {
        notificationOpen: true,
        notificationHeader: 'Second Test',
        notificationBody: 'Second notification after clearing',
        notificationType: 'error',
        notificationPosition: 'top-left',
        notificationDuration: 4000
      };

      service.setNotification(secondNotification);

      expect(service.showAlert).toBe(true);
      expect(service.notification).toEqual(secondNotification);
    });

    it('should handle edge cases and boundary conditions', () => {
      // Test with zero duration
      const zeroDurationNotification = {
        notificationOpen: true,
        notificationHeader: 'Zero Duration',
        notificationBody: 'Notification with zero duration',
        notificationType: 'info',
        notificationPosition: 'top-right',
        notificationDuration: 0
      };

      service.setNotification(zeroDurationNotification);

      expect(service.notification.notificationDuration).toBe(0);
      expect(service.showAlert).toBe(true);

      // Test with very high duration
      const highDurationNotification = {
        notificationOpen: true,
        notificationHeader: 'High Duration',
        notificationBody: 'Notification with very high duration',
        notificationType: 'warning',
        notificationPosition: 'bottom-center',
        notificationDuration: 999999
      };

      service.setNotification(highDurationNotification);

      expect(service.notification.notificationDuration).toBe(999999);
      expect(service.showAlert).toBe(true);
    });
  });
});
