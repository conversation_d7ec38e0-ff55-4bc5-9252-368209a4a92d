import { Component, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { AuthService } from '../../_services/authentication.services';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { RulesApiService } from '../_services/rules-api.service';
import { operatorsMapToShowInQb, OperatorsRulesQB } from '../_services/Rules-QB-Constants';
import { UtilitiesService } from '../../_services/utilities.service';
import { constants } from '../rules-constants';

const FAIL = "Fail";
const WORKSHEET_HEADERS = "Worksheet Headers";

@Component({
  selector: 'app-view',
  templateUrl: './view.component.html',
  styleUrls: ['./view.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class ViewComponent {
  public ruleViewUploadRedraw: any;
  messagingService: any;
  ruleId: any;
  rule: any = {};
  retroApply: boolean = false;
  bypassApply: boolean = false;
  headerLevel: boolean = false;
  public statusDescription: string = "No Status Code Selected";
  openAccordion: boolean = false;
  public labelName: string = "Status*";
  public inputname: string = "inventory-status";
  groupIcon: string = '<i class="fa fa-search" aria-hidden="true"></i>'
  inventoryStatusDataset: any = [];
  filteredResults: any = [];
  public selectedValue: string = "";
  public headerText: string = "";
  public clientData: any = [];
  public conceptData: any = [];
  conceptIdSelected: any = "";
  clientIdSelected: string = "";
  clientIdForECP: any;
  UsersScreenAccessList: any;
  productsList: { name: string; value: string; }[];
  selectedProfileClientId: number;
  isFileUploadTabledata: boolean = false;
  versionSeq: any;
  constructor(private clientApiService: ClientApiService,
    private conceptApiService: ProductApiService, private router: Router, private route: ActivatedRoute, private RulesApiService: RulesApiService,
    private dateService: UtilitiesService, private userManagementSvc: UserManagementApiService, private authService: AuthService) {
    this.ruleId = Number(this.router.url.slice(this.router.url.lastIndexOf("/") + 1));
    this.headerText = `View Rule ${this.ruleId}`;
    this.isFileUploadTabledata = true;
  }
  breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: 'Rules engine', url: '/rules' }, { label: 'View rule' }];

  dataJSON: any;
  public isPriviousRedirectPage = true;
  levelIndicator: string = "Client Level";
  fileUploadType: string = "single";
  fileUploadLabelText: string = "Upload File";
  fileAccept: string = ".png,.xlsx,.pdf,.jpeg";
  fileEnable: boolean = true;
  fileUploadJSON: any = [];
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  displayStyle: any = "none";
  showMessage: boolean = false;
  displayMessage: string = "";
  ruleLevel: string = "";
  relationSHJSON: any[] = [];
  generalDetailsJson: any[] = [];
  additionalDetailsJson: any[];
  ruleTypes: any = [];
  businessOwners: any = [];
  showForms: boolean = false;
  ruleSubTypes: any = [];
  letterType: any = [];
  showHistory: boolean = false;
  ltrRuleSubTypes: any = [];
  ltrWaitDuration: any = [];
  reminderLtrCount: any = [];
  gracePeriod: any = [];
  typeOfdays: any = [];
  provider: any = [];
  concept: any = [];
  letterConcepts: any = [];
  calculationFields: any = [];
  lookBackPeriodValues: any = [];
  laggingPeriodValues: any = [];
  showLoader: boolean = false;
  enableInventoryStatus: boolean = true;
  switchToggleNames: any = { 'onText': 'Value', 'offText': 'CFF' };
  operators: any = OperatorsRulesQB;
  showQueryBuilderComponents: boolean = true;
  customSql: string = "";
  compatibleJsonForConcepts: any = [];
  isConceptDataReady: boolean = true;
  isDraftRule: boolean = false;
  dependentFieldsData: any = [
    {
      hide: ['rule_subtype', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'exception',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'onhold',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'noRecovery',
    },
    {
      hide: ['lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'expiration',
    },
    {
      hide: ['lookup_dates', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'lag',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'letters',
    }
  ];
  dependentLetterData: any = [
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'client_name', 'audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'overpayment',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    }
  ];

  dependentsubRuleData: any = [
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'client_name', "number_of_reminder_letter", 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['audit_type', 'release_by', 'provider', 'lagging_period', 'letter_concept_type', 'max_no_of_claims_per_letter', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'duration',

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidation'

    },
    {
      hide: ['audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'calculation_fields', 'lagging_period', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidations'

    }
  ];
  dependentsubRuleDurationData: any = [
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: 0,

    },
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: null,
    },
  ]

  modalJSON: any = [
    {
      id: "1",
      ruleId: "Rule 1",
      ruleName: "Exclusion Rule",
      ruleType: "Exclusion",
      ruleSubtype: "Exclusion",
      ruleLevel: "Global",
      createdBy: "Chaithra",
      startDate: "12/06/2021",
      endDate: "12/06/2022",
    },
  ];

  modalColumnConfig: any = {
    switches: {
      enableSorting: true,
      enablePagination: false,
      editable: false,
      enableFiltering: false,
    },
    colDefs: [
      {
        name: "ID",
        field: "id",
        filterType: "",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "Rule Id",
        field: "ruleId",
        filterType: "",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "Rule name",
        field: "ruleName",
        filterType: "Text",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "Rule Type",
        field: "ruleType",
        filterType: "",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "Rule Subtype",
        field: "ruleSubtype",
        filterType: "text",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "Rule Level",
        field: "ruleLevel",
        filterType: "text",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "Created By",
        field: "createdBy",
        filterType: "text",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "Start Date",
        field: "startDate",
        filterType: "text",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
      {
        name: "End Date",
        field: "endDate",
        filterType: "text",
        visible: "True",
        editorType: "Text",
        editorTypeRoot: "",
        editorTypeLabel: "",
        editorTypeValue: "",
      },
    ],
  };
  //Table

  querySpecificationJson: any = [];
  customSqlJson: any;
  showCustomSqlJson: boolean = false;

  customFormatterFn(event) {
    return `<a href="${event.dataContext.link}">${event.value}</a>`;
  }

  columnConfigforFileUploadtable: any = {
    switches: {
      enableSorting: true,
      enablePagination: true,
      enableFiltering: true,
    },
    colDefs: [
      {
        "name": "FILE NAME",
        "field": "file_name",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "COMMENTS",
        "field": "comments",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "ATTACHED BY",
        "field": "attached_by",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "SAVED DATE",
        "field": "saved date",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      }
    ]
  };

  multipleCriteriaRule: boolean = false;
  corpusId: string = "";
  customFields: any = [];

  cellValueChanged(event: Event): void { }

  cellClicked(event: any): void { }

  moveToOptionSelected(event: Event): void { }

  onTabSelection(event) {
    setTimeout(() => this.ruleViewUploadRedraw = Date.now(), 100)    
    event.name == 'Rule History' ? this.showHistory = true : "";
  }
  tableReady(event) {

  }

  upload(event: Event): void {

    this.fileUploadJSON = [
      {
        id: "1",
        fileName: "File 1",
        link: "https://www.rapidtables.com/web/html/link/test_file.zip",
        comments: "Testing",
        user: "Chaithra B C",
        savedDate: new Date(),
      },
    ];
  }
  public dropquery = {
  };

  public dragdropconfig: any = {
    fields: {
      conceptID: {
        name: 'ConceptID',
        type: 'numeric',
        mutuallyExclusive: ['client'],
      },
      memberID: { name: 'MemeberID', type: 'text' },
      DOB: { name: 'DOB', type: 'calendar' },
      market: {
        name: 'Market',
        type: 'multipleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      country: {
        name: 'Country',
        type: 'singleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      age: { name: 'age', type: 'numeric', regPattern: '^[0-9]+[0-9]*$' },
      client: {
        name: 'client',
        type: 'text',
        mutuallyExclusive: ['conceptID'],
      },
    },
    validations: {
      unique: ['client', 'conceptID'],
    },
  };
  recentQueryList = [
    {
      Name: "Criteria One",
      "Rule Type": "Global",
      "Rule Sub Type": "Global",
      "Created By": "Lakki Reddy",
      "Created Date": "02/22/2022",
      ruleSet: {
        condition: "and",
        rules: [
          {
            field: "DOB",
            operator: "Equal",
            value: "22",
            static: true,
            active: true,
          },
          {
            field: "client",
            operator: "Less Than",
            value: "100",
            static: true,
            active: false,
          },
        ],
      },
    },
    {
      Name: "Criteria Two",
      "Rule Type": "Global",
      "Rule Sub Type": "Global",
      "Created By": "Ajan Srinivas",
      "Created Date": "02/22/2022",
      ruleSet: {
        condition: "and",
        rules: [
          {
            field: "conceptID",
            operator: "contains",
            value: "55",
            static: true,
            active: true,
          },
          {
            field: "memberID",
            operator: "like",
            value: "400",
            static: true,
            active: false,
          },
        ],
      },
    },
    {
      Name: "Criteria Three",
      "Rule Type": "Regional",
      "Rule Sub Type": "Local",
      "Created By": "User 3",
      "Created Date": "02/22/2022",
      ruleSet: {
        condition: "or",
        rules: [
          {
            field: "DOB",
            operator: "Less Than",
            value: "40",
            static: true,
            active: true,
          },
          {
            field: "client",
            operator: "Greater Than",
            value: "12",
            static: true,
            active: false,
          },
        ],
      },
    },
    {
      Name: "Criteria Four",
      "Rule Type": "Regional",
      "Rule Sub Type": "Local",
      "Created By": "User 4",
      "Created Date": "02/22/2022",
      ruleSet: {
        condition: "or",
        rules: [
          {
            field: "DOB",
            operator: "Greater Than",
            value: "40",
            static: true,
            active: true,
          },
          {
            field: "client",
            operator: "like",
            value: "Smith",
            static: true,
            active: false,
          },
        ],
      },
    },
  ];
  dropRecentList(event) { }

  ngOnInit(): void {
    this.selectedProfileClientId = Number(sessionStorage.getItem('clientId'));
    this.callGetRuleApis();
    this.isFileUploadTabledata = true;
  }
  // function to fetch and show data inside table for View Rule
  callGetFileDetailsRules(ruleLevel, versionSeq) {
    this.RulesApiService.getFileDetailsOfRules(this.ruleId, ruleLevel).subscribe(data => {
      data.result.files?.map((s, i) => {
        s.id = i + 1;
      });
      this.dataJSON = data.result.files;
    })
  }

  callGetRuleApis() {
    this.showLoader = true;
    this.RulesApiService.getAllViewEditRuleAPIs(this.ruleId)
      .subscribe(data => {
        if (data) {
          if (data[0].status?.code == 200 && data[1].status?.code == 200) {
            let masterData = data[0].result.fields;
            let ruleInfo = data[1].result.metadata.rules[0];
            this.isDraftRule = data[1].result.metadata.rules[0].is_draft;
            //uncomment below line and remove line# 455 once E2E is done
            //this.inventoryStatusDataset = data[2];
            this.inventoryStatusDataset = [];
            this.refineMasterData(masterData, ruleInfo);
            this.versionSeq = ruleInfo.version_seq;
            this.callGetFileDetailsRules(ruleInfo.rule_level, this.versionSeq);
          }
          else {
            /* 
            notification part will be covered in the next sprint for success/error messages
            */
            console.log("Unsuccessful", data.status ? data.status.traceback : 'No traceback available');
            this.showLoader = false;
          }
        }
      },
        error => {
          /* 
            notification part will be covered in the next sprint for success/error messages
            */
          this.showLoader = false;
        });
  }
  /**
    * breadcrumSelection Funtion
    */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  refineMasterData(masterDataFromAPI, ruleInfo) {
    let ruleTypeMasterData = masterDataFromAPI['rule_type'];
    let ruleFieldsIdMapping = { "rule_sub_type": "rule_subtype", "calculation_fields": "calculation_fields", "lookback_period": "lookup_dates", "lagging_period": "lagging_period", "letter_type": "letter_type", "provider": "provider", "type_of_days": "type_of_days", "letter_wait_duration_in_days": "letter_wait_duration_in_days", "grace_period_in_days": "grace_period_in_days", "concept": "letter_concept_type", "ltr_rule_sub_type": "ltr_rule_sub_type", "number_of_reminder_letter": "number_of_reminder_letter", "letter_wait_duration_ovp_2": "letter_wait_duration_ovp_2" };
    if (ruleTypeMasterData && Array.isArray(ruleTypeMasterData)) {
      ruleTypeMasterData.forEach(ruleTypeObj => {
      /* All the keys inside ruleTypeObj is rule type */
      Object.keys(ruleTypeObj).forEach((ruleType) => {
        this.ruleTypes.push({ name: ruleType, value: ruleTypeObj[ruleType].value });
        Object.keys(ruleTypeObj[ruleType]).forEach((field) => {
          if (field != 'value') {
            /** Logic to push lookback period value if it is not there in the list */
            if (ruleInfo?.lookup_dates?.value != null && ruleFieldsIdMapping[field] === "lookup_dates") {
              let isElementPresent = false;
              ruleTypeObj[ruleType][field].forEach(element => {
                if (ruleInfo.lookup_dates.value === element.value) {
                  isElementPresent = true;
                }
              });
              if (!isElementPresent) {
                ruleTypeObj[ruleType][field].push({ "name": ruleInfo.lookup_dates.value, "value": Number(ruleInfo.lookup_dates.value) });
              }
            }

            if (ruleInfo.lagging_period != null && ruleFieldsIdMapping[field] === "lagging_period") {
              let isElementPresent = false;
              ruleTypeObj[ruleType][field].forEach(element => {
                if (ruleInfo.lagging_period.value === element.value) {
                  isElementPresent = true;
                }
              });
              if (!isElementPresent) {
                ruleTypeObj[ruleType][field].push({ "name": ruleInfo.lagging_period.value, "value": Number(ruleInfo.lagging_period.value) });
              }
            }

            this.dependentFieldsData.push({
              updateDataset: [
                {
                  id: ruleFieldsIdMapping[field],
                  dataset: ruleTypeObj[ruleType][field],
                },
              ],
              when: ruleTypeObj[ruleType].value,
            });
            if (ruleFieldsIdMapping[field] === "letter_type") {
              let letterdata = ruleTypeObj[ruleType][field];
              letterdata.forEach(letterTypeObj => {
                Object.keys(letterTypeObj).forEach((subFields) => {

                  this.dependentLetterData.push({
                    updateDataset: [
                      {
                        id: ruleFieldsIdMapping[subFields],
                        dataset: letterTypeObj[subFields],
                      },
                    ],
                    when: letterTypeObj['value'],
                  });

                  let subRuleData = letterTypeObj['ltr_rule_sub_type'];
                  subRuleData.forEach(ruleSubObj => {

                    Object.keys(ruleSubObj).forEach((otherFields) => {


                      if (ruleFieldsIdMapping[otherFields] === "letter_wait_duration_in_days" || ruleFieldsIdMapping[otherFields] === "lagging_period" || ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter") {
                        ruleSubObj[otherFields].forEach(element => {
                          element.id = ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter" ? element.value.toString() : element.value;
                        });
                      }

                      if (otherFields == 'number_of_reminder_letter') {
                        Object.keys(ruleSubObj[otherFields][1]).forEach(letterDurationField => {
                          if (ruleFieldsIdMapping[letterDurationField] === "letter_wait_duration_ovp_2") {
                            ruleSubObj[otherFields][1][letterDurationField].forEach(element => {
                              element.id = element.value;
                            });
                          }
                          if (ruleSubObj[otherFields][1][letterDurationField].length > 1) {
                            this.dependentsubRuleDurationData.push({
                              updateDataset: [
                                {
                                  id: ruleFieldsIdMapping[letterDurationField],
                                  dataset: ruleSubObj[otherFields][1][letterDurationField],
                                },
                              ],
                              when: ruleSubObj[otherFields][1]['value'],
                            })
                          }
                        });

                      }

                      this.dependentsubRuleData.push({
                        updateDataset: [
                          {
                            id: ruleFieldsIdMapping[otherFields],
                            dataset: ruleSubObj[otherFields],
                          },
                        ],
                        when: ruleSubObj['value'],
                      });
                    });
                  });

                });
              });
            }
          }
        });
      });
    });
    }
    this.businessOwners = masterDataFromAPI['business_owner'];
    this.dragdropconfig.fields = this.modifyQBConfig(masterDataFromAPI['query_fields']);
    this.populateRuleDataOnForm(ruleInfo);
  }

  modifyQBConfig(masterDataQBConfig) {
    let QBfields = {};
    const typeMapping = {
      'decimal': 'numeric',
      'string': 'text',
      'date': 'calendar'
    };
    if (masterDataQBConfig && Array.isArray(masterDataQBConfig)) {
      masterDataQBConfig.forEach(field => {
      switch (field.field_type) {
        case 'dropdown':
          QBfields[field.value] = { name: field.name, type: 'singleselect', dataset: field.options, key: 'name', id: 'id' };
          break;
        case 'freetext':
          QBfields[field.value] = { name: field.name, type: typeMapping[field.type] };
          if (field.type == 'date') {
            QBfields[field.value].dateFormat = 'YYYY-MM-DD';
          }
      }
    });
    }
    return QBfields;
  }

  /**
   * Method To get static data set for setting inventory status
   */
  getInventoryStatusData() {
    this.RulesApiService.getInventoryStatusData().subscribe((data) => {
      this.inventoryStatusDataset = data;
      setTimeout(() => (this.showDescriptionandInventoryStatus()), 100);
    });
  }

  /**
    * Method To get the description code from db and setting it up to accordion
    */
  showDescriptionandInventoryStatus() {
    this.selectedValue = this.rule.inventory_status ? this.rule.inventory_status : "";
    if (this.selectedValue != undefined && this.selectedValue != "") {
      var target = this.selectedValue.toLowerCase();;
      this.filteredResults = this.inventoryStatusDataset.filter(character => {
        return character.cdValName.toLowerCase().includes(target);
      });
      this.statusDescription = this.filteredResults[0]?.cdValLongDesc ? this.filteredResults[0]?.cdValLongDesc : "No Description Available for Selected Status";
      this.openAccordion = true;
    }
  }

  populateRuleDataOnForm(rule) {
    this.rule = rule;
    this.showForms = true;
    if (rule?.rule_metadata?.corpus_id) {
      this.multipleCriteriaRule = true;
      this.corpusId = rule.rule_metadata.corpus_id;
    }
    this.getDependentDropdownsValues(rule.rule_type);
    this.getDependentDropdownsLtrType(rule.letter_type);
    this.getDependentDropdownsLtrSubType(rule.ltr_rule_sub_type);
    this.getDependentDropdownLtrOVPDuration(rule.number_of_reminder_letter)
    if (rule.execution_type == "sql_query") {
      this.showQueryBuilderComponents = false;
      if (rule.conditions && rule.conditions[0]) {
        this.customSql = rule.conditions[0].query;
      }
    }
    else {
      if (rule.conditions && rule.conditions[0]) {
        this.dropquery = this.modifyQBuilderStructure(rule.conditions[0]);
      }
    }
    this.getAllJsonFilesData();
    this.levelIndicator = rule.rule_level + " Level";
    this.retroApply = rule.retro_apply;
    this.bypassApply = rule.bypass_apply;
    this.headerLevel = rule.header_level;
    this.showDescriptionandInventoryStatus();
    if (!rule.letter_concept_type) {
      rule.letter_concept_type = 'Single';
    }
    if (rule.ltr_rule_sub_type == 'consolidation')
      this.enableInventoryStatus = false;
    this.relationSHJSON = [
      {
        type: 'group',
        name: 'rules',
        label: '',
        column: 1,
        groupControls: [
          {
            type: 'select',
            name: 'rule_type',
            label: 'Rule Type',
            options: this.ruleTypes,
            optionName: 'name',
            optionValue: 'value',
            column: 2,
            closeOnSelect: true,
            id: 'rule_type',
            relationship: this.dependentFieldsData,
            selectedVal: rule.rule_type,
            disabled: true,
          },
          {
            type: 'select',
            name: 'letter_type',
            label: 'Letter Type',
            column: 2,
            id: 'letter_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.letterType,
            relationOptions: [],
            relationship: this.dependentLetterData,
            selectedVal: rule.letter_type,
            disabled: true,
            visible: this.showField("letter_type", rule.rule_type)
          },
          {
            type: 'select',
            name: 'ltr_rule_sub_type',
            label: 'Rule Subtype',
            column: 2,
            id: 'ltr_rule_sub_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.ltrRuleSubTypes,
            relationOptions: [],
            relationship: this.dependentsubRuleData,
            selectedVal: rule.ltr_rule_sub_type,
            disabled: true,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrType("ltr_rule_sub_type", rule.letter_type) : this.showField("ltr_rule_sub_type", rule.rule_type)
          },
          {
            options: this.calculationFields,
            optionName: 'name',
            optionValue: 'value',
            label: 'Calculation Fields',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'calculation_fields',
            column: '2',
            disabled: true,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("calculation_fields", rule.ltr_rule_sub_type) : this.showField("calculation_fields", rule.rule_type),
            id: 'calculation_fields',
            selectedVal: this.isNull(rule.calculation_fields) ? "" : rule.calculation_fields[0],
          },
          {
            type: 'select',
            name: 'rule_subtype',
            label: 'Rule SubType',
            column: 2,
            id: 'rule_subtype',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.ruleSubTypes,
            relationOptions: [],
            visible: this.showField("rule_subtype", rule.rule_type),
            disabled: true,
            selectedVal: rule.rule_subtype
          },
          {
            options: this.reminderLtrCount,
            optionName: 'name',
            optionValue: 'id',
            label: 'Number Of Reminder Letters',
            type: 'select',
            closeOnSelect: true,
            name: 'number_of_reminder_letter',
            column: '2',
            id: 'number_of_reminder_letter',
            required: true,
            disabled: true,
            customTags: true,
            selectedVal: rule.number_of_reminder_letter,
            placeholder: 'Choose Reminder Letters',
            relationship: this.dependentsubRuleDurationData,
            relationOptions: [],
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("number_of_reminder_letter", rule.ltr_rule_sub_type) : this.showField("number_of_reminder_letter", rule.rule_type)
          },
          {
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 1(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_in_days',
            column: '2',
            id: 'letter_wait_duration_in_days',
            customTags: true,
            disabled: true,
            options: this.ltrWaitDuration,
            relationOptions: [],
            selectedVal: rule.letter_wait_duration_in_days,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_wait_duration_in_days", rule.ltr_rule_sub_type) : this.showField("letter_wait_duration_in_days", rule.rule_type)
          },
          {
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 2(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_ovp_2',
            column: '2',
            id: 'letter_wait_duration_ovp_2',
            customTags: true,
            disabled: true,
            options: this.ltrWaitDuration,
            relationOptions: [],
            selectedVal: Number(rule.letter_wait_duration_ovp_2),
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubTypeOVPReminder("letter_wait_duration_ovp_2", rule.number_of_reminder_letter) : this.showField("letter_wait_duration_ovp_2", rule.rule_type)
          },
          {
            label: 'Grace Period(in days)',
            type: 'text',
            name: 'grace_period_in_days',
            column: '2',
            id: 'grace_period_in_days',
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("grace_period_in_days", rule.ltr_rule_sub_type) : this.showField("grace_period_in_days", rule.rule_type),
            disabled: true,
            value: rule.grace_period_in_days,
          },

          {

            optionName: 'name',
            optionValue: 'value',
            label: 'Type of days',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'type_of_days',
            column: '2',
            id: 'type_of_days',
            options: this.typeOfdays,
            disabled: true,
            selectedVal: rule.type_of_days,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("type_of_days", rule.ltr_rule_sub_type) : this.showField("type_of_days", rule.rule_type),
          },
          {
            options: this.lookBackPeriodValues,
            optionName: 'name',
            optionValue: 'value',
            label: 'Lookback Period (in Months)',
            type: 'select',
            closeOnSelect: true,
            name: 'lookup_dates',
            column: '2',
            disabled: true,
            visible: this.showField("lookup_dates", rule.rule_type),
            id: 'lookup_dates',
            selectedVal: rule.lookup_dates?.value ? Number(rule.lookup_dates.value) : '',
          },
          {
            options: this.laggingPeriodValues,
            optionName: 'name',
            optionValue: 'value',
            label: 'Lagging Period (in Days)',
            type: 'select',
            closeOnSelect: true,
            name: 'lagging_period',
            column: '2',
            disabled: true,
            visible: this.showField('lagging_period', rule.rule_type),
            id: 'lagging_period',
            required: true,
            selectedVal: rule.lagging_period ? Number(rule.lagging_period.value) : '',
          },
          {
            options: [
              {
                name: rule.client,
                id: rule.clientId,
              }
            ],
            optionName: 'name',
            optionValue: 'name',
            label: 'Client Name',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'client_name',
            column: '2',
            disabled: true,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("client_name", rule.ltr_rule_sub_type) : this.showField("client_name", rule.rule_type),
            id: 'client_name',
            required: true,
            selectedVal: rule.client_name,
          },
          {
            optionName: 'name',
            optionValue: 'value',
            label: 'Provider',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'provider',
            column: '2',
            disabled: true,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("provider", rule.ltr_rule_sub_type) : this.showField("provider", rule.rule_type),
            id: 'provider',
            required: true,
            options: this.provider,
            selectedVal: rule.provider,
          },
          {

            optionName: 'name',
            optionValue: 'value',
            label: 'Concept',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'letter_concept_type',
            column: '2',
            disabled: true,
            options: this.letterConcepts,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_concept_type", rule.ltr_rule_sub_type) : this.showField("letter_concept_type", rule.rule_type),
            id: 'letter_concept_type',
            selectedVal: rule.letter_concept_type,

          },
          {
            label: 'Max no Of Claims Per Letter',
            type: 'text',
            name: 'max_no_of_claims_per_letter',
            column: '2',
            disabled: true,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("max_no_of_claims_per_letter", rule.ltr_rule_sub_type) : this.showField("max_no_of_claims_per_letter", rule.rule_type),
            id: 'max_no_of_claims_per_letter',
            value: rule.max_no_of_claims_per_letter
          },
          {
            options: [
              {
                name: 'Audit',
                value: 'audit',
              },
              {
                name: 'Provider Audit',
                value: 'providerAudit',
              },
              {
                name: 'Report Audit',
                value: 'reportAudit',
              },
              {
                name: 'Over Payment',
                value: 'overPayment',
              },
              {
                name: 'Business Audit',
                value: 'businessAudit',
              },
            ],
            optionName: 'name',
            optionValue: 'value',
            label: 'Inventory Type',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'audit_type',
            column: '2',
            disabled: true,
            visible: this.showField('audit_type', rule.rule_type),
            id: 'audit_type',
            required: true,
            selectedVal: rule.audit_type,
          },
          {
            label: 'Release By',
            type: 'date',
            name: 'release_by',
            id: 'release_by',
            column: '2',
            disabled: true,
            visible: this.showField('release_by', rule.rule_type),
            pickerType: 'single',
            required: true,
            dateFormat: 'MM-DD-YYYY',
            value: this.dateService.getDbgDateFormat(rule.release_by),
          },
        ],
      },
    ];

    this.generalDetailsJson = [
      {
        type: 'group',
        name: 'generalDetailsLeft',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Rule Name',
            type: 'text',
            name: 'rule_name',
            column: '1',
            disabled: true,
            value: rule.rule_name,
          },
          {
            label: 'Rule Description',
            type: 'textarea',
            name: 'description',
            column: '1',
            disabled: true,
            value: rule.description,
          },
          {
            label: 'Term Reason',
            type: 'text',
            name: 'term_reason',
            column: '1',
            disabled: true,
            value: rule.term_reason,
          },
          !this.isDraftRule && {
            label: 'Reason For Edit',
            type: 'textarea',
            name: 'edit_reason',
            column: '1',
            disabled: true,
            value: rule.edit_reason,
            placeholder: ''
          },
        ],
      },
      {
        type: 'group',
        name: 'generalDetailsRight',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Start Date',
            type: 'date',
            name: 'start_date',
            column: '3',
            disabled: true,
            value: this.dateService.getDbgDateFormat(rule.start_date),
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY',
          },
          {
            label: 'End Date',
            type: 'date',
            name: 'end_date',
            column: '3',
            disabled: true,
            pickerType: 'single',
            value: this.dateService.getDbgDateFormat(rule.end_date),
            dateFormat: 'MM-DD-YYYY',
          },
          {
            label: 'Status',
            type: 'text',
            name: 'status',
            column: '3',
            disabled: true,
            value: String(rule.status) == "true" ? 'Active' : 'Inactive',
          },
          {
            label: 'Review Reminder Date',
            type: 'date',
            name: 'review_remainder_date',
            column: '1',
            disabled: true,
            pickerType: 'single',
            value: rule.review_remainder_date,
          },
          {
            label: 'Business Owner',
            type: 'text',
            name: 'business_owner',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: rule.business_owner,
          },
          !this.isDraftRule && {
            label: 'Comments',
            type: 'textarea',
            name: 'comments',
            id: 'comments',
            column: '1',
            groupColumn: '1',
            disabled: true,
            value: rule.comments,
            required: false
          },
        ],
      },
    ];
    this.additionalDetailsJson = [
      {
        type: 'group',
        name: 'additionalDetailsTop',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'External point of contact',
            group: '',
            type: 'text',
            name: 'external_point_of_contact',
            id: 'external_point_of_contact',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: rule.external_point_of_contact,
          },
        ],
      },
      {
        type: 'group',
        name: 'additionalDetailsBottom',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'Created by',
            group: '',
            type: 'text',
            name: 'created_by',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: rule.created_by,
          },
          {
            label: 'Created date',
            group: '',
            type: 'date',
            name: 'created_ts',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: this.dateService.getDbgDateFormat(rule.created_ts),
            pickerType: 'single',
          },
          {
            label: 'Updated by',
            group: '',
            type: 'text',
            name: 'updated_by',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: rule.updated_by,
          },
          {
            label: 'Updated date',
            group: '',
            type: 'date',
            name: 'updated_ts',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: rule['updated date'] ? this.dateService.getDbgDateFormat(rule['updated date']) : this.dateService.getDbgDateFormat(rule['updated_ts']),
            pickerType: 'single',
          },
        ],
      },
    ];
  }

  getDependentDropdownsValues(conditionKey) {
    this.dependentFieldsData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {

            case 'rule_subtype':
              this.ruleSubTypes = dependentField.dataset;
              break;
            case 'letter_type':
              this.letterType = dependentField.dataset;
              break;
            case 'calculation_fields':
              this.calculationFields = dependentField.dataset;
              break;
            case 'lookup_dates':
              this.lookBackPeriodValues = dependentField.dataset;
              this.lookBackPeriodValues.forEach(fieldValue => {
                fieldValue.value = fieldValue.value;
              });
              break;
            case 'lagging_period':
              this.laggingPeriodValues = dependentField.dataset;
              this.laggingPeriodValues.forEach(fieldValue => {
                fieldValue.value = fieldValue.value;
              });
              break;
          }
        });
      }
    });
  }


  getDependentDropdownsLtrType(conditionKey) {
    this.dependentLetterData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'ltr_rule_sub_type':
              this.ltrRuleSubTypes = dependentField.dataset;
              break;
          }
        });
      }
    });
  }

  getDependentDropdownsLtrSubType(conditionKey) {
    this.dependentsubRuleData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'letter_wait_duration_in_days':
              this.ltrWaitDuration = dependentField.dataset;
              break;
            case 'number_of_reminder_letter':
              this.reminderLtrCount = dependentField.dataset;
              break;
            case 'type_of_days':
              this.typeOfdays = dependentField.dataset;
              break;
            case 'calculation_fields':
              this.calculationFields = dependentField.dataset;
              break;
            case 'provider':
              this.provider = dependentField.dataset;
              break;
            case 'letter_concept_type':
              this.letterConcepts = dependentField.dataset;
              break;
          }
        });
      }
    });
  }
  getDependentDropdownLtrOVPDuration(conditionKey) {
    this.dependentsubRuleDurationData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'letter_wait_duration_ovp_2':
              this.ltrWaitDuration = dependentField.dataset;
              break;
          }
        });
      }
    });
  }

  showField(field, conditionKey) {
    let makeItVisible = true;
    this.dependentFieldsData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });


      }
    });
    return makeItVisible;
  }

  showFieldLtrType(field, conditionKey) {
    let makeItVisible = true;

    this.dependentLetterData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }

  showFieldLtrSubType(field, conditionKey) {
    let makeItVisible = true;

    this.dependentsubRuleData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }

  showFieldLtrSubTypeOVPReminder(field, conditionKey) {
    let makeItVisible = true;
    this.dependentsubRuleDurationData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }


  modifyQBuilderStructure(qbQuery) {
    const operatorMap = operatorsMapToShowInQb;
    let customFields = [];
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case 'log':
          this.condition = v;
          break;
        case 'conditions':
          this.rules = v;
          break;
        case 'lval':
          this.field = v;
          break;
        case 'rval':
          this.value = v;
          this.startValue = v.start;
          this.endValue = v.end;
          customFields.push(v);
          break;
        case 'op':
          this.operator = operatorMap[v];
          break;
        case 'config':
        case 'operatorList':
        case 'delete':
        case 'json_path':
          delete qbQuery[k];
          break;
        default:
          return v;
      }
    });
    this.pushCustomFieldsToQBConfig(customFields);
    return parsed;
  }

  /**
   * checks for null value
  */
  isNull(fieldValue) {
    if (fieldValue == null || fieldValue == "") return true;
    else return false;
  }

  ngAfterViewInit(): void {
    const collection = document.querySelectorAll(
      "marketplace-dynamic-form button"
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].remove();
    }
  }

  /**
   * Method will be called on click of download button 
   * and calls service API to get the file
   */
  DownloadMultiCriteriaFile() {
    this.showLoader = true;
    this.RulesApiService.getMultipleCriteriaFile(this.ruleId, this.corpusId, this.levelIndicator).subscribe(data => {
      this.showLoader = false;
      this.generateExceldata(data, "multi_criteria_file");
    },
      error => {
        this.showLoader = false;
      });
  }

  /**
   * Invoked when excel export icon is clicked
   * @function generateExceldata generates a excel file
   * @param data response from API
   * @param fileName fileName
   * @returns excelFile
   */
  generateExceldata(data: any, fileName: any) {
    if (Object.keys(data).length) {
      let a = document.createElement("a");
      a.id = "excel";
      a.href = "data:text/csv," + data.body;
      a.setAttribute("download", fileName + ".csv");
      document.body.appendChild(a);
      a.download = fileName;
      a.click();
    }
  }

  /**
   * Method pushes cusom fields to query Builder config
   */
  pushCustomFieldsToQBConfig(customFields): void {
    this.dragdropconfig.customFieldList = {};
    this.dragdropconfig.customFieldList.dataset = [];
    customFields.forEach(column => {
      this.dragdropconfig.customFieldList.dataset.push({ "name": column, "id": column, "collection": WORKSHEET_HEADERS });
    });
  }

  /**
    * triggers when chip is closed
   */
  closeStateChip(selectedState) {
    this.isConceptDataReady = false;
    this.compatibleJsonForConcepts = this.compatibleJsonForConcepts.filter(x => x !== selectedState);
    this.conceptIdSelected = this.compatibleJsonForConcepts;
    this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'conceptId')[0].selectedVal = this.compatibleJsonForConcepts;
    setTimeout(() => {
      this.isConceptDataReady = true;
    }, 0);
  }

  /**
   * Get all json file data and assign it to respective objevts
   */
  getAllJsonFilesData(): void {
    this.showLoader = true;
    this.isConceptDataReady = false;
    this.RulesApiService.getAssetsJson(constants.RULE_QUERY_SPEC_JSON).subscribe((data) => {
      this.querySpecificationJson = data.sqlStructure;
      this.querySpecificationJson[0].value = this.showQueryBuilderComponents ? "qb" : "custSql";
      let tokenVal = localStorage?.getItem("token")
      let _clientData = this.clientApiService.getAllClientsInPreferenceCenter();
      let _conceptData = this.conceptApiService.getProductConceptsId(tokenVal);

      forkJoin([_clientData, _conceptData]).subscribe(([clientData, conceptData]) => {
        if (clientData && Array.isArray(clientData)) {
          this.clientData = clientData.map(x => ({ value: x.clientId, name: x.clientName }));
          if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
            const clientControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'clientId')[0];
            if (clientControl) {
              clientControl.options = this.clientData;
            }
          }
        }

        if (conceptData && conceptData.executionConceptAnalyticResponse) {
          this.conceptData = conceptData.executionConceptAnalyticResponse.map(x => ({ id: x.exConceptReferenceNumber, name: x.exConceptReferenceNumber }));
          if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
            const conceptControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'conceptId')[0];
            if (conceptControl) {
              conceptControl.options = this.conceptData;
            }
          }
        }
        if (this.rule.concept && this.rule.concept.length >= 1 && this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
          this.compatibleJsonForConcepts = this.rule.concept;

          const rulesLevelControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'rulesLevel')[0];
          const conceptIdControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'conceptId')[0];

          if (rulesLevelControl) {
            rulesLevelControl.selectedVal = "Concept Level";
            rulesLevelControl.disabled = true;
          }
          if (conceptIdControl) {
            conceptIdControl.selectedVal = this.compatibleJsonForConcepts;
            conceptIdControl.disabled = true;
            conceptIdControl.visible = true;
          }
        } else if (this.rule.clientId && this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
          const rulesLevelControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'rulesLevel')[0];
          const clientIdControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'clientId')[0];

          if (rulesLevelControl) {
            rulesLevelControl.selectedVal = "Client Level";
            rulesLevelControl.disabled = true;
          }
          if (clientIdControl) {
            clientIdControl.selectedVal = this.rule.clientId;
            clientIdControl.visible = true;
            clientIdControl.disabled = true;
          }
        }
        else if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
          const rulesLevelControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0];
          const rulesLevelControl2 = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'rulesLevel')[0];

          if (rulesLevelControl) rulesLevelControl.selectedVal = constants.GLOBAL_LEVEL;
          if (rulesLevelControl2) rulesLevelControl2.disabled = true;
        }
        if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
          const rulesLevelControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0];
          const selectedRuleLevel = rulesLevelControl ? rulesLevelControl.selectedVal : null;
          if (this.selectedProfileClientId === 59 && selectedRuleLevel !== constants.GLOBAL_LEVEL) {
            const productControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.PRODUCT)[0];
            if (productControl) productControl.visible = true;
          }
        }
        this.isConceptDataReady = true;
        this.showLoader = false

      }, error => {
        this.clientData = [];
        this.showLoader = false;
      });

      if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
        const rulesLevelControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'rulesLevel')[0];
        if (rulesLevelControl) {
          rulesLevelControl.disabled = true;
        }
      }

      if (this.querySpecificationJson[0]) {
        this.querySpecificationJson[0].value = this.showQueryBuilderComponents ? "qb" : "custSql";
        if (this.querySpecificationJson[0].options) {
          this.querySpecificationJson[0].options.map(c => c.enabled = false);
        }
        this.querySpecificationJson[0].disabled = true;
      }

      if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
        if (this.querySpecificationJson[1].groupControls[0]) {
          this.querySpecificationJson[1].groupControls[0].disabled = true;
        }
        if (this.querySpecificationJson[1].groupControls[1]) {
          this.querySpecificationJson[1].groupControls[1].disabled = true;
        }
        if (this.querySpecificationJson[1].groupControls[2]) {
          this.querySpecificationJson[1].groupControls[2].disabled = true;
        }
      }

      if (!this.showQueryBuilderComponents) {
        this.customSqlJson = data.customSQL.filter(c => c.type != "textarea");
        this.customSqlJson[0]['groupControls'][0].disabled = true;
        // this.showQuerySpec = true;
        if (this.rule.clientId) {
          this.customSqlJson[0]['groupControls'][0].selectedVal = constants.CLIENT_LEVEL;
          this.customSqlJson[0]['groupControls'][1].options = [{ name: this.rule.client, value: this.rule.clientId }];
          this.customSqlJson[0]['groupControls'][1].selectedVal = this.rule.clientId;
          this.customSqlJson[0]['groupControls'][1].visible = true;
          this.customSqlJson[0]['groupControls'][1].disabled = true;
        }
        else if (this.rule.concept) {
          this.customSqlJson[0]['groupControls'][0].selectedVal = constants.CONCEPT_LEVEL;
          this.customSqlJson[0]['groupControls'][2].visible = true;
          this.customSqlJson[0]['groupControls'][2].value = this.rule.concept;
          this.customSqlJson[0]['groupControls'][2].disabled = true;
        }
      }

      setTimeout(() => {
        this.showCustomSqlJson = true;
      }, 0);
    });
  }

}