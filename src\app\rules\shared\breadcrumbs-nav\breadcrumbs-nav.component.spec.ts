import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { BreadcrumbsNavComponent } from './breadcrumbs-nav.component';
import { AuthService } from '../../../_services/authentication.services';

describe('BreadcrumbsNavComponent', () => {
  let component: BreadcrumbsNavComponent;
  let fixture: ComponentFixture<BreadcrumbsNavComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockAuthService: jasmine.SpyObj<AuthService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    routerSpy.url = '/rules/breadcrumbs';
    const authServiceSpy = { isWriteOnly: false };

    await TestBed.configureTestingModule({
      declarations: [BreadcrumbsNavComponent],
      imports: [HttpClientTestingModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(BreadcrumbsNavComponent);
    component = fixture.componentInstance;

    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
  });

  beforeEach(() => {
    // Mock window.history.back
    spyOn(window.history, 'back');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.isPriviousRedirectPage).toBe(false);
      expect(component.breadcrumbDataset).toEqual([]);
    });

    it('should accept input properties', () => {
      component.headerText = 'Test Header';
      component.isPriviousRedirectPage = true;
      component.breadcrumbDataset = [
        { label: 'Home', url: '/' },
        { label: 'Rules', url: '/rules' }
      ];

      expect(component.headerText).toBe('Test Header');
      expect(component.isPriviousRedirectPage).toBe(true);
      expect(component.breadcrumbDataset.length).toBe(2);
    });
  });

  describe('ngOnInit', () => {
    it('should call ngOnInit without errors', () => {
      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('Navigation Methods', () => {
    it('should navigate to selected breadcrumb URL', () => {
      const mockEvent = {
        selected: { url: '/test-url' }
      };

      component.breadcrumSelection(mockEvent);

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/test-url']);
    });

    it('should handle breadcrumb selection with different URLs', () => {
      const mockEvent1 = { selected: { url: '/rules' } };
      const mockEvent2 = { selected: { url: '/dashboard' } };

      component.breadcrumSelection(mockEvent1);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules']);

      component.breadcrumSelection(mockEvent2);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should go back to previous page', () => {
      component.backToPreviousPage();

      expect(window.history.back).toHaveBeenCalled();
    });
  });

  describe('Input Properties', () => {
    it('should handle headerText input', () => {
      const testHeader = 'Custom Header Text';
      component.headerText = testHeader;

      expect(component.headerText).toBe(testHeader);
    });

    it('should handle isPriviousRedirectPage input', () => {
      component.isPriviousRedirectPage = true;
      expect(component.isPriviousRedirectPage).toBe(true);

      component.isPriviousRedirectPage = false;
      expect(component.isPriviousRedirectPage).toBe(false);
    });

    it('should handle breadcrumbDataset input', () => {
      const testDataset = [
        { label: 'Home', url: '/' },
        { label: 'Rules Engine', url: '/rules' },
        { label: 'Create Rule', url: '/rules/create' }
      ];

      component.breadcrumbDataset = testDataset;

      expect(component.breadcrumbDataset).toEqual(testDataset);
      expect(component.breadcrumbDataset.length).toBe(3);
    });

    it('should handle empty breadcrumbDataset', () => {
      component.breadcrumbDataset = [];

      expect(component.breadcrumbDataset).toEqual([]);
      expect(component.breadcrumbDataset.length).toBe(0);
    });
  });

  describe('Edge Cases', () => {
    it('should handle breadcrumb selection with null event', () => {
      expect(() => component.breadcrumSelection(null)).toThrow();
    });

    it('should handle breadcrumb selection with undefined selected property', () => {
      const mockEvent = { selected: undefined };

      expect(() => component.breadcrumSelection(mockEvent)).toThrow();
    });

    it('should handle breadcrumb selection with empty URL', () => {
      const mockEvent = { selected: { url: '' } };

      component.breadcrumSelection(mockEvent);

      expect(mockRouter.navigate).toHaveBeenCalledWith(['']);
    });
  });

  describe('Component Dependencies', () => {
    it('should have Router service injected', () => {
      expect(mockRouter).toBeDefined();
    });

    it('should have AuthService injected', () => {
      expect(mockAuthService).toBeDefined();
    });
  });

  describe('Additional Method Coverage', () => {
    it('should handle ngOnInit lifecycle hook', () => {
      expect(() => component.ngOnInit()).not.toThrow();
    });

    it('should handle backToPreviousPage method', () => {
      component.backToPreviousPage();
      expect(window.history.back).toHaveBeenCalled();
    });

    it('should handle component initialization with different breadcrumbDataset values', () => {
      component.breadcrumbDataset = [
        { label: 'Home', url: '/' },
        { label: 'Rules', url: '/rules' },
        { label: 'Dashboard', url: '/rules/dashboard' }
      ];

      expect(component.breadcrumbDataset.length).toBe(3);
      expect(component.breadcrumbDataset[0].label).toBe('Home');
      expect(component.breadcrumbDataset[1].url).toBe('/rules');
    });

    it('should handle empty breadcrumbDataset', () => {
      component.breadcrumbDataset = [];
      expect(component.breadcrumbDataset.length).toBe(0);
    });

    it('should handle breadcrumbDataset with single item', () => {
      component.breadcrumbDataset = [{ label: 'Single', url: '/single' }];
      expect(component.breadcrumbDataset.length).toBe(1);
      expect(component.breadcrumbDataset[0].label).toBe('Single');
    });

    it('should handle breadcrumb selection with complex URLs', () => {
      const mockEvent = { selected: { url: '/rules/create?param=value&test=123' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules/create?param=value&test=123']);
    });

    it('should handle breadcrumb selection with hash URLs', () => {
      const mockEvent = { selected: { url: '/rules#section1' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules#section1']);
    });

    it('should handle breadcrumb selection with relative URLs', () => {
      const mockEvent = { selected: { url: '../parent' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['../parent']);
    });

    it('should handle multiple breadcrumb selections', () => {
      const mockEvent1 = { selected: { url: '/rules' } };
      const mockEvent2 = { selected: { url: '/rules/dashboard' } };

      component.breadcrumSelection(mockEvent1);
      component.breadcrumSelection(mockEvent2);

      expect(mockRouter.navigate).toHaveBeenCalledTimes(2);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules']);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules/dashboard']);
    });

    it('should handle breadcrumb selection with numeric URLs', () => {
      const mockEvent = { selected: { url: '/rules/123' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules/123']);
    });

    it('should handle component properties correctly', () => {
      expect(component.authService).toBeDefined();
      expect(Array.isArray(component.breadcrumbDataset)).toBe(true);
    });

    it('should handle breadcrumbDataset property changes', () => {
      const initialDataset = component.breadcrumbDataset;
      const newDataset = [{ label: 'New', url: '/new' }];

      component.breadcrumbDataset = newDataset;
      expect(component.breadcrumbDataset).toBe(newDataset);
      expect(component.breadcrumbDataset).not.toBe(initialDataset);
    });

    it('should handle authService dependency', () => {
      expect(component.authService).toBe(mockAuthService);
    });

    it('should handle breadcrumb selection with whitespace URLs', () => {
      const mockEvent = { selected: { url: '   /rules   ' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['   /rules   ']);
    });

    it('should handle breadcrumb selection with encoded URLs', () => {
      const mockEvent = { selected: { url: '/rules/test%20space' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules/test%20space']);
    });

    it('should handle component state consistency', () => {
      const originalDataset = component.breadcrumbDataset;

      // Perform some operations
      component.breadcrumSelection({ selected: { url: '/test' } });

      // Dataset should remain unchanged
      expect(component.breadcrumbDataset).toBe(originalDataset);
    });

    it('should handle breadcrumb selection with nested object structure', () => {
      const mockEvent = {
        selected: {
          url: '/rules',
          metadata: { id: 1, name: 'test' }
        }
      };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules']);
    });

    it('should handle breadcrumb selection with special characters in URL', () => {
      const mockEvent = { selected: { url: '/rules/test-123_special!@#' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules/test-123_special!@#']);
    });

    it('should handle breadcrumb selection error scenarios', () => {
      mockRouter.navigate.and.throwError('Navigation error');
      const mockEvent = { selected: { url: '/error' } };

      expect(() => component.breadcrumSelection(mockEvent)).toThrow();
    });

    it('should handle headerText property', () => {
      component.headerText = 'Test Header';
      expect(component.headerText).toBe('Test Header');

      component.headerText = null;
      expect(component.headerText).toBeNull();
    });

    it('should handle isPriviousRedirectPage property', () => {
      expect(component.isPriviousRedirectPage).toBe(false);

      component.isPriviousRedirectPage = true;
      expect(component.isPriviousRedirectPage).toBe(true);

      component.isPriviousRedirectPage = false;
      expect(component.isPriviousRedirectPage).toBe(false);
    });

    it('should handle breadcrumbDataset initialization', () => {
      expect(Array.isArray(component.breadcrumbDataset)).toBe(true);
      expect(component.breadcrumbDataset.length).toBe(0);
    });

    it('should handle breadcrumbDataset with complex objects', () => {
      const complexDataset = [
        { label: 'Home', url: '/', icon: 'home', active: true },
        { label: 'Rules', url: '/rules', icon: 'rules', active: false },
        { label: 'Dashboard', url: '/dashboard', icon: 'dashboard', active: false }
      ];

      component.breadcrumbDataset = complexDataset;
      expect(component.breadcrumbDataset.length).toBe(3);
      expect(component.breadcrumbDataset[0].icon).toBe('home');
      expect(component.breadcrumbDataset[1].active).toBe(false);
    });

    it('should handle breadcrumb selection with null selected object', () => {
      const mockEvent = { selected: null };
      expect(() => component.breadcrumSelection(mockEvent)).toThrow();
    });

    it('should handle breadcrumb selection with missing url property', () => {
      const mockEvent = { selected: { label: 'Test' } };
      // This actually doesn't throw because it accesses event.selected.url which is undefined
      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['undefined']);
    });

    it('should handle multiple calls to ngOnInit', () => {
      expect(() => {
        component.ngOnInit();
        component.ngOnInit();
        component.ngOnInit();
      }).not.toThrow();
    });

    it('should handle breadcrumb selection with zero-length URL', () => {
      const mockEvent = { selected: { url: '' } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['']);
    });

    it('should handle breadcrumb selection with boolean false URL', () => {
      const mockEvent = { selected: { url: false } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['false']);
    });

    it('should handle breadcrumb selection with number zero URL', () => {
      const mockEvent = { selected: { url: 0 } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['0']);
    });

    it('should handle component constructor dependencies', () => {
      expect(component.authService).toBeDefined();
      expect(component.authService).toBe(mockAuthService);
    });

    it('should handle breadcrumbDataset property type checking', () => {
      // Test with different data types
      component.breadcrumbDataset = null;
      expect(component.breadcrumbDataset).toBeNull();

      component.breadcrumbDataset = undefined;
      expect(component.breadcrumbDataset).toBeUndefined();

      component.breadcrumbDataset = [];
      expect(component.breadcrumbDataset).toEqual([]);
    });

    it('should handle headerText with different data types', () => {
      component.headerText = 123;
      expect(component.headerText).toBe(123);

      component.headerText = true;
      expect(component.headerText).toBe(true);

      component.headerText = { title: 'test' };
      expect(component.headerText).toEqual({ title: 'test' });
    });

    it('should handle breadcrumb selection with array URL', () => {
      const mockEvent = { selected: { url: ['/rules', 'dashboard'] } };

      component.breadcrumSelection(mockEvent);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules,dashboard']);
    });
  });
});
