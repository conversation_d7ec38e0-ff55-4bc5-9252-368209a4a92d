import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router"
import { RulesApiService } from '../_services/rules-api.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { OperatorsRulesQB, operatorsMapToShowInQb, OperatorsMapForQb } from '../_services/Rules-QB-Constants';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { CookieService } from 'ngx-cookie-service';
import { constants } from '../rules-constants';
import { RegistrationConstants } from 'src/app/registration/constants';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { forkJoin } from 'rxjs';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { ToastService } from 'src/app/_services/toast.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { BusinessDivisionService } from 'src/app/_services/business-division.service';

const STANDARD = "Standard";
const UPLOAD_FILE = "Upload File";
const ANTM_ID = 59;
const FAIL = "Fail";
const FILE_UPLOAD_MSG = "File successfully uploaded. You may proceed with the rule creation.";
const SUCCESS = "Success";
const SELECTED_PRODUCT = "Data Mining";
const qbQueryDefault = {
  condition: 'and',
  rules: [
    {
      field: '',
      operator: 'Equal',
      value: '',
      static: true,
      active: true,
    }
  ]
}
const WORKSHEET_HEADERS = "Worksheet Headers";

@Component({
  selector: 'app-copy',
  templateUrl: './copy.component.html',
  styleUrls: ['./copy.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class CopyComponent implements OnInit {

  ruleEditUploadRedraw: any;
  ruleId: number;
  public headerText: string = '';
  inventoryStatusDataset: any = [];
  showMaxLimitMsg: boolean = false;
  enableInventoryStatus: boolean = true;
  noResultsFound: boolean = false;
  public clientData: any = [];
  public conceptData: any = [];
  conceptIdSelectedForChips: any = [];
  UsersScreenAccessList: any;
  product: string;
  businessUnit: string;
  businessDivision: string;
  productsList: { name: string; value: string; }[];
  openImpactReportPopup: boolean = false;
  openbypassConfirm: boolean = false;
  constructor(
    private clientApiService: ClientApiService,
    private conceptApiService: ProductApiService,
    private router: Router,
    private route: ActivatedRoute,
    private RulesApiService: RulesApiService,
    private dateService: UtilitiesService,
    private alertService: ToastService,
    private cookieService: CookieService,
    private userManagementSvc: UserManagementApiService,
    private authService: AuthService,
    private businessDivisionService: BusinessDivisionService
  ) {
    this.ruleId = Number(this.router.url.slice(this.router.url.lastIndexOf('/') + 1));
    this.headerText = "Create New Rule";
    this.getInventoryStatusData();
    this.userId = this.cookieService.get(ROUTING_LABELS.USER_ID).toUpperCase();
  }
  breadcrumbDataset = [
    { label: 'Home', url: '/' },
    { label: 'Rules engine', url: '/rules' },
    { label: 'Copy rule' },
  ];
  public kebabOptions: any = [
    { label: '<i class="fa fa-eye" aria-hidden="true"></i> XXX ', id: 'view' },
    { label: '<i class="fa fa-eye" aria-hidden="true"></i> XXX ', id: 'edit' },
    {
      label: '<i class="fa fa-eye" aria-hidden="true"></i> XXX ',
      id: 'delete',
    },
  ];
  isFileReady: boolean = false;
  isTextReady: boolean = false;
  dataJSON: any;
  showQueryBuilderComponents: boolean = false;
  customSql: string = "";
  customSqlJson: any;
  querySpecificationJson: any = [];
  showQuerySpec: boolean = false;
  querySpecDetailsResponse: any = {};
  querySpecDetailsFormEvent: any = {};
  modalColumnConfigDuplicate: any = "./assets/json/duplicateRules.json";
  public labelName: string = "Status*";
  public inputname: string = "inventory-status";
  filteredResults: any = [];
  groupIcon: string = '<i class="fa fa-search" aria-hidden="true"></i>'
  public selectedValue: string = "";
  public statusDescription: string = "No Status Code Selected";
  public statusSuggestion: string = "";
  public statusInfo: string = "";
  scrollItems: any = 'none';
  openAccordion: boolean = false;
  searchResultsWindow: boolean = false;
  suggestionWindow: boolean = false;
  columnConfigDuplicatePopup: any;
  public fileUploadEditJSON: any;
  public isDisabled: any = true;
  duplicateRuleTableJson: any = [];
  public isLoading: any = false;
  public postUploadDataJson: any;
  public tableRedraw: any;
  public isPriviousRedirectPage = true;
  levelIndicator: string = '';
  fileUploadType: string = 'multiple';
  fileUploadLabelText: string = 'Upload File';
  fileAccept: string = '.png,.xlsx,.pdf,.jpeg,.PNG,.XLSX,.PDF,.JPEG';
  fileEnable: boolean = true;
  fileUploadJSON: any = [];
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  displayStyle: any = 'none';
  fileUploadPopup: any = 'none';
  popupDisplayStyle: any = 'none';
  notificationOpen: boolean = false;
  notificationHeader: string = '';
  notificationBody: string = '';
  notificationType: any = '';
  notificationPosition: any = 'top-right';
  notificationDuration: number = 3000;
  showMessage: boolean = false;
  displayMessage: string = '';
  ruleLevel: string = '';
  isRuleLevelPresent: boolean = false;
  setStatusOfRuleLevel: boolean = false;
  displayDuplicateMessage: boolean = false;
  mainDetailsResponse: any = {};
  generalDetailsResponse: any = {};
  additionalDetailsResponse: any = {};
  public editFormData: any = {};
  httpRequestdata: any = {
    url: './assets/json/form.json',
    dataRoot: 'src',
  };
  mainDetailsFormEvent: any;
  generalDetailsFormEvent: any;
  additionalDetailsFormEvent: any;
  retroApply: boolean = false;
  bypassApply: boolean = false;
  headerLevel: boolean = false;
  relationSHJSON: any[] = [];
  isFormSubmitted: boolean = false;
  ruleSubmitButton: boolean = true;
  generalDetailsJson: any[] = [];
  additionalDetailsJson: any[];
  ruleTypes: any = [];
  businessOwners: any = [];
  showForms: boolean = false;
  ruleSubTypes: any = [];
  letterType: any = [];
  ltrRuleSubTypes: any = [];
  ltrWaitDuration: any = [];
  gracePeriod: any = [];
  typeOfdays: any = [];
  provider: any = [];
  concept: any = [];
  letterConcepts: any = [];
  calculationFields: any = [];
  lookBackPeriodValues: any = [];
  laggingPeriodValues: any = [];
  reminderLtrCount: any = [];
  ltrWaitDurationOvp2: any = [];
  rule: any = {};
  isEdited: boolean = false;
  showLoader: boolean = false;
  fileDetailsExcelOpenModel: boolean = false;
  switchToggleNames: any = { 'onText': 'Value', 'offText': 'CFF' };
  operators: any = OperatorsRulesQB;
  userId: string = "";
  queryBuilderClientDataset: any = [];
  createOpenPopup: any = false;
  createErrorOpenPopup: any = false;
  createUploadOpenPopup: boolean = false;
  openFileUploadConfirmModal: boolean = false;
  openConfirmationModal: boolean = false;
  clientIdQBConfigEntryBackUp: any = {};
  clientIdSelected: string = "";
  clientIdForECP: any;
  conceptIdSelected: any = [];
  selectedProfileClientId: number;
  selectedProfileClientName: string = "";
  public updatedRuleId: any;
  masterDataFromAPI: any;
  ruleLevelFormEvent: any;
  isDraft: boolean = false;
  inventoryStatusOptions = {
    expiration: 'Expired',
    exception: 'Excluded',
    onhold: 'On Hold',
    noRecovery: 'No Recovery',
    lag: 'Lag Wait'
  };
  dependentFieldsData: any = [
    {
      hide: ['rule_subtype', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'exception',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'onhold',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'noRecovery',
    },
    {
      hide: ['lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'expiration',
    },
    {
      hide: ['lookup_dates', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'lag',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'letters',
    }
  ];
  dependentLetterData: any = [
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'client_name', 'audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'overpayment',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    }
  ];

  dependentsubRuleData: any = [
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'client_name', "number_of_reminder_letter", 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['audit_type', 'release_by', 'provider', 'lagging_period', 'letter_concept_type', 'max_no_of_claims_per_letter', 'letter_wait_duration_ovp_2'],
      when: 'duration',

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidation'

    },
    {
      hide: ['audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'calculation_fields', 'lagging_period', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidations'

    }
  ];
  dependentsubRuleDurationData: any = [
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: 0,

    },
  ]

  onTabSelection(event) {
    setTimeout(() => (this.ruleEditUploadRedraw = Date.now()), 100);
  }
  /**
   * breadcrumSelection Funtion
   */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  public qbQuery = {
    condition: 'and',
    rules: [
      {
        field: '',
        operator: 'Equal',
        value: '',
        static: true,
        active: true,
      }
    ],
  };

  public qbLetterQuery = {
    condition: 'and',
    rules: [
      {
        field: 'CLNT_ID',
        operator: 'Equal',
        value: 65,
        static: true,
        active: true,
      }
    ],
  };

  qbConfig: any = {
    fields: {
      conceptID: {
        name: 'ConceptID',
        type: 'numeric',
        mutuallyExclusive: ['client'],
      },
      memberID: { name: 'MemeberID', type: 'text' },
      DOB: { name: 'DOB', type: 'calendar' },
      market: {
        name: 'Market',
        type: 'multipleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      country: {
        name: 'Country',
        type: 'singleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      age: { name: 'age', type: 'numeric', regPattern: '^\\d+$', regexMsg: 'Enter only positive numbers', forceRegex: true },
      client: {
        name: 'client',
        type: 'text',
        mutuallyExclusive: ['conceptID'],
      },
    },
    validations: {
      unique: ['CLNT_ID', 'CNCPT_ID']
    }
  };

  fileDetailsSectionJson: any[] = [
    {
      label: 'Comments',
      group: '',
      type: 'textarea',
      name: 'comments',
      column: '2',
      groupColumn: '1',
      disabled: false,
      value: '',
      placeholder: 'Enter comments here...',
      required: true
    },
  ];

  columnConfigforFileUploadtable: any = {
    switches: {
      enableSorting: true,
      enablePagination: true,
      enableFiltering: true,
    },
    colDefs: [
      {
        "name": "FILE NAME",
        "field": "file_name",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "COMMENTS",
        "field": "comments",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "ATTACHED BY",
        "field": "attached_by",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "SAVED DATE",
        "field": "saved date",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      }
    ]
  };

  multipleCriteriaRule: boolean = false;
  corpusId: string = "";
  customFields: any = [];
  sgDashboardDataset: any = [
    { id: 'standardQB', label: 'Standard', checked: true },
    { id: 'uploadQB', label: 'Upload File' }
  ];
  isStandardQBSelected: boolean = true;
  showSubmit: boolean = true;
  multiCriteriaFile: any = {};
  showQBuilder: boolean = true;
  unSelectedIndex: number = 0;
  showSegmentedControl: boolean = true;
  disableUploadBtn: boolean = true;
  uploadFileStatus: string = "";
  uploadFileStatusMsg: string = "";
  fileParserFileAccept: string = '.csv';
  fileParserTableProperties: any = {
    "enableSorting": false,
    "enablePagination": true,
    "editable": false,
    "enableFiltering": false,
    "isExcelExportNeeded": false,
    "isToggleColumnsNeeded": false,
    "isAddNeeded": false,
    "isSaveNeeded": false,
    "isDeleteNeeded": false,
    "isBulkUploadNeeded": false,
    "isRowSelectable": false
  }
  qbFilled: boolean = true;
  cellValueChanged(event: Event): void { }

  cellClicked(event: any): void { }

  /**
   * shows the upload file modal
  */
  uploadFileInEditRule() {
    this.fileDetailsExcelOpenModel = true;
    this.isFileReady = true;
    this.isTextReady = true;
    this.fileUploadPopup = 'none';
    this.showMessage = false;
    this.displayDuplicateMessage = true;
    this.showMaxLimitMsg = false;
    this.fileUploadPopup = 'block';
  }

  fileDetailsExcelClosePopup = () => {
    this.fileDetailsExcelOpenModel = false;
  }

  /**
   * Resets upload file modal
  */
  fileUploadpopUpReset() {
    this.isFileReady = false;
    this.isTextReady = false;
    this.fileUploadPopup = "none";
    this.cancelEdit();
  }

  /**
   * closes the upload file modal
  */
  closePopupUploadForEditRule() {
    this.fileUploadpopUpReset();
  }

  onSubmitSkipClicked() {
    this.createUploadOpenPopup = false;
    this.openImpactReportPopup = true;
  }

  /**
   * Triggers on change event of dynamic form
  */
  mapValuesToUploadJson(event: any) {
    this.postUploadDataJson = {
      "commentsInUpload": event.value['comments']
    }
    this.checkValidationForUploadFile();
  }

  /**
  * Validate the form to make submit button enable/disable
*/
  checkValidationForUploadFile() {
    this.isDisabled = true;
    if (this.fileUploadEditJSON !== undefined && this.postUploadDataJson.commentsInUpload !== undefined && this.fileUploadEditJSON !== "" && this.postUploadDataJson.commentsInUpload.trim() !== '' && !this.showMaxLimitMsg) {
      this.isDisabled = false;
    }
  }

  /**
   * Function to get Duplicate Data Json
   */
  getConfigForDuplicateRules() {
    this.RulesApiService.getColumnConfigJsonDuplicate(this.modalColumnConfigDuplicate).subscribe((data) => {
      this.columnConfigDuplicatePopup = data;
    })
  }

  /**
    * Validate the file size to make submit button enable/disable
    * Disable the Submit button if file size exceeds 25mb = 26214400bytes
  */
  validateMaxFileSize() {
    let combinedSize = 0;
    Object.keys(this.fileUploadEditJSON).forEach(key => {
      combinedSize += this.fileUploadEditJSON[key].size;
    });
    let isFileDisable = (combinedSize > 26214400) ? true : false;
    return isFileDisable;
  }

  /**
   * Triggers on click of submit on file uplaod modal
  */
  onSubmitUploadClicked() {
    this.isLoading = true;
    const formData = new FormData();
    Object.keys(this.fileUploadEditJSON).forEach(key => {
      formData.append("file", this.fileUploadEditJSON[key]);
    });
    formData.append("solution_id", 'claimsol1');
    formData.append("rule_id", this.updatedRuleId);
    formData.append("comments", this.postUploadDataJson.commentsInUpload);
    formData.append("attached_by", this.userId);
    formData.append("saved_date", this.dateService.formatDate());
    this.createUploadOpenPopup = false;
    this.RulesApiService.addFilesToRules(formData, this.levelIndicator.split(" ")[0])
      .subscribe(
        data => {
          if (data) {
            this.isLoading = false;
            this.alertService.setSuccessNotification({
              notificationHeader: "Success",
              notificationBody: 'File successfully attached to the rule ' + this.updatedRuleId,
            });
            this.openImpactReportPopup = true;
          }
        },
        error => {
          this.isLoading = false;
          this.alertService.setErrorNotification({
            notificationHeader: "Fail",
            notificationBody: `${error.statusText}`,
          });
        });
    this.isFileReady = false;
    this.isTextReady = false;
    this.fileUploadPopup = "none";
  }

  /**
   * Gets triggered on click event on upload button
  */
  upload(event: Event): void {
    this.fileUploadEditJSON = event;
    let fileSizeMaxedOut = this.validateMaxFileSize();
    if (fileSizeMaxedOut) {
      this.showMaxLimitMsg = true;
    }
    else {
      this.showMaxLimitMsg = false;
    }
    this.checkValidationForUploadFile();
  }

  /**
   * To remove highlighting from fieds which passed validation
  */
  resetValidFields() {
    const collection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-valid ,
      marketplace-dynamic-form .ng-select.ng-select-single.ng-valid .ng-select-container,
      marketplace-dynamic-form .ng-select.ng-select-multiple.ng-valid .ng-select-container,
      marketplace-textarea.ng-valid .textarea-holder textarea,
      marketplace-date-picker.ng-valid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].classList.remove('redBorder');
    }
  }

  /**
   * To highlight fieds which failed validation
  */
  showAllInvalidFields() {
    this.createErrorOpenPopup = true;
    this.resetValidFields();
    const invalidCollection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-invalid ,
      marketplace-dynamic-form .ng-select.ng-select-single.ng-invalid .ng-select-container,
      marketplace-dynamic-form .ng-select.ng-select-multiple.ng-valid .ng-select-container,
      marketplace-textarea.ng-invalid .textarea-holder textarea,
      marketplace-date-picker.ng-invalid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < invalidCollection.length; i++) {
      invalidCollection[i].classList.add('redBorder');
    }
    if (this.isNull(this.selectedValue)) {
      let statusField = document.getElementsByName("inventory-status");
      statusField.forEach(field => field.classList.add('redBorder'));
    }
    this.popupDisplayStyle = 'block';
  }

  editErrClosePopup = () => {
    this.editErrOpenModel = false;
  }

  /**
   * checks for null value
  */
  isNull(fieldValue) {
    if (fieldValue == null || fieldValue == "") return true;
    else return false;
  }

  /**
   * Changing the query builder structure to be sync with API
  */
  modifyQBuilderStructure(qbQuery) {
    const operatorMap = OperatorsMapForQb;
    let startVal;
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case "condition":
          this.log = v;
          break;
        case "rules":
          this.conditions = v;
          break;
        case "field":
          this.json_path = "$";
          this.lval = v;
          break;
        case "startValue":
          startVal = JSON.parse(JSON.stringify(v));
          this.rval = { "start": startVal, "end": '' }
          break;
        case "endValue":
          this.rval = { "start": startVal, "end": v }
          break;
        case "value":
          if (v.toString().indexOf(',') > -1) {
            this.rval = v.toString().split(',');
          }
          else {
            this.rval = v;
          }
          break;
        case "operator":
          this.op = operatorMap[v];
          break;
        case "config":
        case "operatorList":
        case "delete":
        case "fieldsList":
        case "fieldsMapList":
        case "customfieldsList":
        case "tabsList":
          delete qbQuery[k];
          break;
        default:
          return v;

      }
    });
    return parsed;
  }
  editErrOpenModel: boolean = false
  /**
   * Validating all the dynamic forms for the mandatory fields 
   * and highlighting the failed fields by calling showAllInvalidFields()
  */
  validateCreateDynamicForms(buttonType: string) {

    if (buttonType == 'submit' && this.bypassApply) {
      this.openbypassConfirm = true;
    }
    else {
      this.openbypassConfirm = false;
      if (buttonType == 'submitbypass') {
        buttonType = 'submit';
        this.closebypassConfirm();
      }
      let isFormTouched = !this.isDefined(this.mainDetailsFormEvent) || !this.isDefined(this.generalDetailsFormEvent);
      let isRuleLevelValid = true;
      if (!this.showQueryBuilderComponents && this.levelIndicator == constants.CLIENT_LEVEL) {
        isRuleLevelValid = this.ruleLevelFormEvent ? this.ruleLevelFormEvent['controls']['group']['controls']['clientId']['status'] == constants.VALID : false;
      }
      else if (!this.showQueryBuilderComponents && this.levelIndicator == constants.CONCEPT_LEVEL) {
        isRuleLevelValid = this.ruleLevelFormEvent ? this.ruleLevelFormEvent['controls']['group']['controls']['conceptId']['status'] == constants.VALID : false;
      }
      let ruleLevelStatus = this.ruleLevelFormEvent.controls.group.controls
      this.setStatusOfRuleLevel = true;
      if (ruleLevelStatus.rulesLevel.status == 'VALID' && ruleLevelStatus.rulesLevel.value == 'Global Level') {
        this.setStatusOfRuleLevel = false;
      } else {
        if (ruleLevelStatus.rulesLevel.value == 'Client Level' && ruleLevelStatus.clientId.status == 'VALID') {
          this.setStatusOfRuleLevel = false;
        } else if (ruleLevelStatus.rulesLevel.value == 'Concept Level' && ruleLevelStatus.conceptId.status == 'VALID') {
          this.setStatusOfRuleLevel = false;
        }
      }
      this.isDraft = (buttonType === 'save') ? true : false;

      if (!this.isDefined(this.mainDetailsFormEvent) && !this.isDefined(this.generalDetailsFormEvent) && isRuleLevelValid && this.levelIndicator) {
        this.validateCreate();
        return;
      }
      else if ((this.isDefined(this.mainDetailsFormEvent) && this.mainDetailsFormEvent['status'] == constants.INVALID) ||
        (this.isDefined(this.generalDetailsFormEvent) && this.generalDetailsFormEvent['status'] == constants.INVALID) ||
        this.isNull(this.selectedValue) || !isRuleLevelValid || !this.levelIndicator || this.setStatusOfRuleLevel) {
        this.showAllInvalidFields();
        return;
      } else {
        this.resetValidFields();
      }
      this.formCreateObjectWithFormData();
    }
  }

  /**
   * method to close the bypass confirmation popup
   */
  closebypassConfirm(): void {
    this.openbypassConfirm = false;
  }

  /**
   * method moves all the changes form values to the rule object
   */
  formCreateObjectWithFormData(): void {
    Object.keys(this.mainDetailsResponse).forEach((group) => {
      Object.keys(this.mainDetailsResponse[group]['value']).forEach(
        (formControl) => {
          this.rule[formControl] = this.mainDetailsResponse[group]['value'][formControl];
        }
      );
    });
    if (this.isDefined(this.generalDetailsFormEvent)) {
      Object.keys(this.generalDetailsResponse).forEach((group) => {
        Object.keys(this.generalDetailsResponse[group]['value']).forEach(
          (formControl) => {
            this.rule[formControl] = this.generalDetailsResponse[group]['value'][formControl];
          }
        );
      });
    }
    if (this.isDefined(this.additionalDetailsFormEvent)) {
      this.rule['external_point_of_contact'] = this.additionalDetailsResponse['additionalDetailsTop']['value']['external_point_of_contact'];
    }
    this.validateCreate();
  }

  /**
  * Validate query builder fields and evaluate rule level
  */
  validateCreate() {
    if (this.levelIndicator == constants.GLOBAL_LEVEL) {
      this.createOpenPopup = true;
      this.showMessage = true;
      this.displayDuplicateMessage = false;
      this.displayMessage =
        'You are about to create a Global Rule that will affect all clients, concepts and insights.';
      this.displayStyle = 'block';
    } else {
      this.createRule();
    }
  }

  /**
   * Making flag true to show duplicate modal
  */
  checkForDuplicateRules() {
    this.createOpenPopup = true;
    this.showMessage = false;
    this.displayDuplicateMessage = true;
    this.displayStyle = 'block';
    setTimeout(() => (this.tableRedraw = Date.now()), 100);
  }

  /**
   * Which closes the current screen and show dashboard
  */
  cancelEdit() {
    this.router.navigate([`${this.breadcrumbDataset[1].url}`]);
  }

  /**
   * closes all the modal
  */
  closePopup() {
    this.createErrorOpenPopup = false;
    this.createOpenPopup = false;
    this.displayStyle = 'none';
    this.popupDisplayStyle = 'none';
    this.showLoader = false;
  }

  /**
   * set the retro aplly property to a local variable
  */
  setRetro(event) {
    this.rule['retro_apply'] = event.toggle;
    this.isEdited = true;
  }

  /**
   * set the bypass property to a local variable
  */
  setBypass(event) {
    this.rule['bypass_apply'] = event.toggle;
    this.bypassApply = event.toggle;
    this.isEdited = true;
  }

  /**
* set the headerlevel property to a local variable
*/
  setLevel(event) {
    this.rule['header_level'] = event.toggle;
    this.headerLevel = event.toggle;
    this.isEdited = true;
  }

  editSubmitOpenModel: boolean = false;

  /**
   * Calls the service method to call Edit API
  */
  createRule() {
    this.closePopup();
    this.showLoader = true;
    this.rule.request_type = "create";
    this.rule.inventory_status = this.selectedValue;
    delete this.rule.status;
    delete this.rule.rule_id;
    delete this.rule.updated_by;
    delete this.rule['updated date'];
    delete this.rule.solution_id;
    this.rule.rule_level = this.levelIndicator?.split(" ")[0];
    if (this.rule.calculation_fields) this.rule.calculation_fields = [this.rule.calculation_fields];
    if (this.rule.lookup_dates) this.rule.lookup_dates = { "value": this.rule.lookup_dates, "type": "month" };
    if (this.rule.lagging_period) this.rule.lagging_period = { "value": this.rule.lagging_period, "type": "day" };
    this.rule.retro_apply = this.retroApply;
    this.rule.bypass_apply = this.bypassApply;
    this.rule.header_level = this.headerLevel;
    this.rule.created_by = this.userId;
    this.rule.updated_ts = "";
    this.rule.start_date = this.dateService.getECPDateFormat(this.rule.start_date);
    this.rule.end_date = this.dateService.getECPDateFormat(this.rule.end_date);

    this.rule.grace_period_in_days = this.rule.grace_period_in_days != "" ? this.rule.grace_period_in_days : null;
    this.rule.max_no_of_claims_per_letter = this.rule.max_no_of_claims_per_letter != "" ? this.rule.max_no_of_claims_per_letter : null;

    if (this.clientIdSelected == "" && this.rule.client_name) {
      this.clientIdSelected = this.selectedProfileClientName;
    }
    if (this.levelIndicator == constants.CLIENT_LEVEL && this.clientIdSelected != "") {
      this.rule.client = this.clientIdSelected;
      this.rule.clientId = this.clientIdForECP;
    }
    if ((this.levelIndicator == constants.CONCEPT_LEVEL || this.rule.rule_type == "lag") && this.conceptIdSelected != "") {
      this.rule.concept = this.conceptIdSelected;
      this.rule.client = this.selectedProfileClientName;
      this.rule.clientId = this.selectedProfileClientId;
    }

    if (this.levelIndicator == constants.GLOBAL_LEVEL && !this.rule.client_name) {
      this.rule.clientId = null;
      this.rule.client = null;
      this.rule.concept = null;
      this.rule.client_name = null;
    }
    else if ((this.levelIndicator == constants.GLOBAL_LEVEL && this.rule.client_name) || this.levelIndicator == constants.CLIENT_LEVEL) {
      this.rule.concept = null;
    }
    else if (this.levelIndicator == constants.CONCEPT_LEVEL && !this.rule.client_name) {
      this.rule.client_name = null;
      this.rule.client = this.selectedProfileClientName;
      this.rule.clientId = this.selectedProfileClientId;
    }
    this.rule.concept = this.conceptIdSelected;
    //commenting because of not seeing any point. Will be removed once testing is done
    // this.rule.client = this.clientIdSelected;
    // this.rule.clientId = this.clientIdForECP;
    if (this.levelIndicator == constants.CLIENT_LEVEL && this.rule.clientId == ANTM_ID) {
      this.rule.business_area_name = this.businessDivisionService.getBusinessDivision().toUpperCase() + SELECTED_PRODUCT;
    }
    else {
      delete this.rule.business_area_name;
    }
    this.rule.conditions = [];
    if (this.rule.rule_type == "letters") {
      //removed once testing is done
      //var tstval = this.qbQuery.rules.filter(f => f.value == "");
      //var tstselectval = this.qbQuery.rules.filter(f => f.value == "select");
      this.qbLetterQuery.rules[0].value = this.selectedProfileClientId;
      if (this.qbQuery.rules.length == 0)
        this.rule.conditions.push(this.modifyQBuilderStructure(this.qbLetterQuery));
      else
        this.rule.conditions.push(this.modifyQBuilderStructure(this.qbQuery));
    }
    else if (this.rule.rule_type == "expiration" && this.showQueryBuilderComponents && this.qbQuery.rules.length == 0) {
      this.qbLetterQuery.rules[0].value = this.selectedProfileClientId;
      this.rule.conditions.push(this.modifyQBuilderStructure(this.qbLetterQuery));
    }
    else {
      if (this.showQueryBuilderComponents)
        this.rule.conditions.push(this.modifyQBuilderStructure(this.qbQuery));
      else {
        this.rule.conditions.push({ "query": this.customSql });
        this.rule.execution_type = "sql_query";
      }
    }
    if (this.corpusId != "") {
      this.rule.rule_metadata = { "corpus_id": this.corpusId };
    }
    this.rule.is_draft = this.isDraft;
    let editRuleRequest = {
      data: this.rule,
      "created_ts": this.dateService.getDbgDateFormat(Date.now())
    };

    this.RulesApiService.createEditRule(editRuleRequest).subscribe(
      (data) => {
        let duplicateSwitchCase: any;
        duplicateSwitchCase = data.duplicates_present == true ? true : data.status.code == 200 ? 200 : 500;
        switch (duplicateSwitchCase) {
          case true:
            this.duplicateRuleTableJson = [];
            let getDuplicateResults = [];
            let mergingResults: any = data.duplicate_rules;
            this.duplicateRuleTableJson = getDuplicateResults.concat(mergingResults);
            this.duplicateRuleTableJson.map((s, i) => {
              s.id = i + 1;
              if (s.rule_type == 'letters')
                s.rule_subtype = s.ltr_rule_sub_type;
            });
            this.checkForDuplicateRules();
            break;
          case 200:
            this.updatedRuleId = data.result.metadata.rule_id;
            this.alertService.setSuccessNotification({
              notificationHeader: this.isDraft ? constants.RULE_SAVED_MESSAGE : constants.RULE_SUBMISSION_MESSAGE,
              notificationBody: `Rule Id : ${data.result.metadata.rule_id}`,
            });
            this.uploadFileInCreateRule();
            break;
          case 500:
            this.alertService.setErrorNotification({
              notificationBody: data.status.message,
            });
            break;
          default:
            break;
        }
      },
      (error) => {
        this.alertService.setErrorNotification({
          notificationBody: error,
        });
        this.showLoader = false;
      }
    );
  }

  editSubmitClosePopup = () => {
    this.editSubmitOpenModel = false;
  }

  recentQueryList = [
    {
      Name: 'Criteria One',
      'Rule Type': 'Global',
      'Rule SubType': 'Global',
      'Created By': 'Lakki Reddy',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'and',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '22',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: '100',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Two',
      'Rule Type': 'Global',
      'Rule SubType': 'Global',
      'Created By': 'Ajan Srinivas',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'and',
        rules: [
          {
            field: 'conceptID',
            operator: 'Equal',
            value: '55',
            static: true,
            active: true,
          },
          {
            field: 'memberID',
            operator: 'Equal',
            value: '400',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Three',
      'Rule Type': 'Regional',
      'Rule SubType': 'Local',
      'Created By': 'User 3',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'or',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '40',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: '12',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Four',
      'Rule Type': 'Regional',
      'Rule SubType': 'Local',
      'Created By': 'User 4',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'or',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '40',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: 'Smith',
            static: true,
            active: true,
          },
        ],
      },
    },
  ];



  /**
   * triggers on change of mainForm dynamic form
  */
  mapValuesFromMainToJson(event) {
    this.mainDetailsResponse = event.controls;
    this.mainDetailsFormEvent = event;

    this.rule['release_by'] = this.mainDetailsResponse.rules.value.release_by;
    this.isEdited = true;
    let ruleTypeForValidation = this.mainDetailsFormEvent.value.rules.rule_type;
    if (this.isDefined(ruleTypeForValidation)) {
      switch (ruleTypeForValidation) {
        case 'expiration':
          this.selectedValue = this.inventoryStatusOptions.expiration;
          break;
        case 'exception':
          this.selectedValue = this.inventoryStatusOptions.exception;
          break;
        case 'onhold':
          this.selectedValue = this.inventoryStatusOptions.onhold;
          break;
        case 'noRecovery':
          this.selectedValue = this.inventoryStatusOptions.noRecovery;
          break;
        case 'lag':
          this.selectedValue = this.inventoryStatusOptions.lag;
          break;
        case 'letters':
          {
            this.selectedValue = "Awaiting Adjustment";
            this.suggestionWindow = false;
            if (this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != null && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidation' && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidations') {
              this.enableInventoryStatus = true;
            }
            else
              this.enableInventoryStatus = false;
            if (this.mainDetailsFormEvent.value.rules.letter_type == "overpayment")
              this.selectedValue = "Awaiting Adjustment";
            else if (this.mainDetailsFormEvent.value.rules.letter_type == "disregard")
              this.selectedValue = "Closed";

            break;
          }
        default:
          break;
      }
    }
    else {
      this.enableInventoryStatus = false;
    }
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * triggers on change of generalForm dynamic form
  */
  mapValuesFromGeneralToJson(event) {
    this.generalDetailsResponse = event.controls;
    this.generalDetailsFormEvent = event;
    this.isEdited = true;
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * triggers on change of additionalForm dynamic form
  */
  mapValuesFromAdditionalToJson(event) {
    this.additionalDetailsResponse = event.controls;
    this.additionalDetailsFormEvent = event;
    this.isEdited = true;
    setTimeout(() => (this.resetValidFields(), 100));
  }

  ngOnInit(): void {
    this.selectedProfileClientId = Number(sessionStorage.getItem('clientId'));
    this.selectedProfileClientName = sessionStorage.getItem('clientName');
    this.callGetRuleApis();
    this.getAllJsonFilesData();
    this.callGetFileDetailsRules();
    this.getConfigForDuplicateRules();
  }

  /**
   * Calls the service method to get files for the rule from API
  */
  callGetFileDetailsRules() {
    this.RulesApiService.getFileDetailsOfRules(this.ruleId, this.levelIndicator.split(" ")[0],).subscribe(data => {
      if (data) {
        if (data.status.code == 200 && this.isDefined(data.result) && this.isDefined(data.result.files)) {
          data.result.files.map((s, i) => {
            s.id = i + 1;
          });
          this.dataJSON = data.result.files;
        }
      }
    })
  }

  /**
   * Calls the service method to get rule info and master data from API
  */
  callGetRuleApis() {
    this.showLoader = true;
    this.RulesApiService.getAllViewEditRuleAPIs(this.ruleId).subscribe(
      (data) => {
        if (data) {
          if (data[0].status.code == 200 && data[1].status.code == 200) {
            let masterData = data[0].result.fields;
            let ruleInfo = data[1].result.metadata.rules[0];
            this.refineMasterData(masterData, ruleInfo);
          } else {
            /* 
          notification part will be covered in the next sprint for success/error messages
          */
            console.log('Unsuccessful', data.status.traceback);
            this.showLoader = false;
          }
        }
      },
      (error) => {
        /* 
              notification part will be covered in the next sprint for success/error messages
          */
        this.showLoader = false;
      }
    );
  }

  /**
   * Refine master data into different objects for different fields
  */
  refineMasterData(masterDataFromAPI, ruleInfo) {
    this.masterDataFromAPI = masterDataFromAPI;
    let ruleTypeMasterData = masterDataFromAPI['rule_type'];
    let ruleFieldsIdMapping = { rule_sub_type: "rule_subtype", calculation_fields: "calculation_fields", lookback_period: "lookup_dates", lagging_period: "lagging_period", "letter_type": "letter_type", "provider": "provider", "type_of_days": "type_of_days", "letter_wait_duration_in_days": "letter_wait_duration_in_days", "grace_period_in_days": "grace_period_in_days", "concept": "letter_concept_type", "ltr_rule_sub_type": "ltr_rule_sub_type", "number_of_reminder_letter": "number_of_reminder_letter", "letter_wait_duration_ovp_2": "letter_wait_duration_ovp_2" };
    ruleTypeMasterData.forEach((ruleTypeObj) => {
      /* All the keys inside ruleTypeObj is rule type */
      Object.keys(ruleTypeObj).forEach((ruleType) => {
        this.ruleTypes.push({
          name: ruleType,
          value: ruleTypeObj[ruleType].value,
        });
        Object.keys(ruleTypeObj[ruleType]).forEach((field) => {
          if (field != 'value') {
            /** Logic to push lookback period value if it is not there in the list */
            if (ruleInfo?.lookup_dates?.value != null && ruleFieldsIdMapping[field] === "lookup_dates") {
              let isElementPresent = false;
              ruleTypeObj[ruleType][field].forEach(element => {
                if (ruleInfo.lookup_dates.value === element.value) {
                  isElementPresent = true;
                }
              });
              if (!isElementPresent) {
                ruleTypeObj[ruleType][field].push({ "name": ruleInfo.lookup_dates.value, "value": Number(ruleInfo.lookup_dates.value) });
              }
            }

            if (ruleInfo?.lagging_period != null && ruleFieldsIdMapping[field] === "lagging_period") {
              let isElementPresent = false;
              ruleTypeObj[ruleType][field].forEach(element => {
                if (ruleInfo.lagging_period.value === element.value) {
                  isElementPresent = true;
                }
              });
              if (!isElementPresent) {
                ruleTypeObj[ruleType][field].push({ "name": ruleInfo.lagging_period.value, "value": Number(ruleInfo.lagging_period.value) });
              }
            }

            this.dependentFieldsData.push({
              updateDataset: [
                {
                  id: ruleFieldsIdMapping[field],
                  dataset: ruleTypeObj[ruleType][field],
                },
              ],

              when: ruleTypeObj[ruleType].value,
            });

            if (ruleFieldsIdMapping[field] === "letter_type") {
              let letterdata = ruleTypeObj[ruleType][field];
              letterdata.forEach(letterTypeObj => {
                Object.keys(letterTypeObj).forEach((subFields) => {

                  this.dependentLetterData.push({
                    updateDataset: [
                      {
                        id: ruleFieldsIdMapping[subFields],
                        dataset: letterTypeObj[subFields],
                      },
                    ],
                    when: letterTypeObj['value'],
                  });

                  let subRuleData = letterTypeObj['ltr_rule_sub_type'];
                  subRuleData.forEach(ruleSubObj => {

                    Object.keys(ruleSubObj).forEach((otherFields) => {


                      if (ruleFieldsIdMapping[otherFields] === "letter_wait_duration_in_days" || ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter" || ruleFieldsIdMapping[otherFields] === "lagging_period") {
                        ruleSubObj[otherFields].forEach(element => {
                          element.id = ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter" ? element.value.toString() : element.value;
                        });
                      }
                      //Field reuired only for medica and KC client
                      if (![constants.BCBSKC_CLIENT_ID, constants.MEDICA_CLIENT_ID].includes(Number(sessionStorage.getItem('clientId')))) {
                        this.dependentsubRuleData.forEach(subRuleData => {

                          if (subRuleData['when'] === 'duration' && subRuleData.hide) {
                            subRuleData.hide.push('number_of_reminder_letter');
                          }
                        });
                      }

                      if (otherFields == 'number_of_reminder_letter') {
                        Object.keys(ruleSubObj[otherFields][1]).forEach(letterDurationField => {
                          if (ruleFieldsIdMapping[letterDurationField] === "letter_wait_duration_ovp_2") {
                            ruleSubObj[otherFields][1][letterDurationField].forEach(element => {
                              element.id = element.value;
                            });
                          }
                          if (ruleSubObj[otherFields][1][letterDurationField].length > 1) {
                            this.dependentsubRuleDurationData.push({
                              updateDataset: [
                                {
                                  id: ruleFieldsIdMapping[letterDurationField],
                                  dataset: ruleSubObj[otherFields][1][letterDurationField],
                                },
                              ],
                              when: ruleSubObj[otherFields][1]['value'],
                            })
                          }
                        });

                      }

                      this.dependentsubRuleData.push({
                        updateDataset: [
                          {
                            id: ruleFieldsIdMapping[otherFields],
                            dataset: ruleSubObj[otherFields],
                          },
                        ],
                        when: ruleSubObj['value'],
                      });
                    });
                  });

                });
              });
            }
          }
        });
      });
    });
    // this.businessOwners = masterDataFromAPI['business_owner'];
    this.qbConfig.fields = this.modifyQBConfig(masterDataFromAPI['query_fields']);
    delete this.qbConfig.fields.CLNT_ID;
    delete this.qbConfig.fields.CNCPT_ID;
    this.populateRuleDataOnForm(ruleInfo);
    this.setClientConcept();
  }

  /**
   * Modify master data fields into the format query builder understands
  */
  modifyQBConfig(masterDataQBConfig) {
    let QBfields = {};
    let mutuallyExclusiveFields = { 'CLNT_ID': 'CNCPT_ID', 'CNCPT_ID': 'CLNT_ID', 'CNCPT_NM': 'CLNT_ID' };
    const typeMapping = {
      'decimal': 'numeric',
      'string': 'text',
      'date': 'calendar'
    };
    masterDataQBConfig.forEach(field => {
      switch (field.field_type) {
        case 'dropdown':
          QBfields[field.value] = { name: field.name, type: 'singleselect', dataset: field.options, key: 'name', id: 'id' };
          break;
        case 'freetext':
          QBfields[field.value] = { name: field.name, type: typeMapping[field.type] };
          if (field.type == 'date') {
            QBfields[field.value].dateFormat = 'YYYY-MM-DD';
          }
          if (field.type == 'decimal') {
            QBfields[field.value].regPattern = '^\\d+(\\.\\d)?\\d*$';
            QBfields[field.value].forceRegex = true;
          }
      }
      if (field.value == 'CLNT_ID' || field.value == 'CNCPT_ID' || field.value == 'CNCPT_NM') {
        QBfields[field.value].mutuallyExclusive = [mutuallyExclusiveFields[field.value]];
      }
      if (field.value == 'CLNT_ID') {
        let clientDropdownValues = field.options.find((item) => item.id === this.selectedProfileClientId);
        QBfields[field.value].dataset = [clientDropdownValues];
        this.queryBuilderClientDataset = [clientDropdownValues];
      }
    });
    return QBfields;
  }

  /**
   * Method TO show Description and similar codes according to Status Selected
   */
  onSelect(item) {
    this.isEdited = true;
    this.statusDescription = item?.cdValLongDesc ? item?.cdValLongDesc : "No Description Available for Selected Status";
    this.statusSuggestion = item.cdValShrtDesc;
    this.selectedValue = item.cdValName;
    this.searchResultsWindow = false;
    this.suggestionWindow = false;
    this.openAccordion = true;
  }
  /**
   * Method To get static data set for setting inventory status
   */
  getInventoryStatusData() {
    this.RulesApiService.getInventoryStatusData().subscribe((data) => {
      this.inventoryStatusDataset = data;

      setTimeout(() => (this.showDescriptionandInventoryStatus()), 100);
    });
  }

  /**
   * Method TO close search results if input box loses focus
   */
  inventoryInputfocusOut(ev: any) {
    if (this.filteredResults.length == 0) {
      setTimeout(() => (this.selectedValue = "", 100));
    }
    this.noResultsFound = false;
  }

  /**
 * Method To populate accordion with description according to status code selected
 */
  giveDescriptionForStatus(event: any) {
    var checkingField = event.target?.value ? event.target.value : "";
    if (checkingField != "") {
      var target = event.target.value.toLowerCase();
      this.filteredResults = this.inventoryStatusDataset.filter(character => {
        return character.cdValName.toLowerCase().includes(target);
      });
      this.searchResultsWindow = true;
      this.openAccordion = false;
      if (this.filteredResults.length == 1) {
        this.suggestionWindow = true;
        this.statusDescription = this.filteredResults[0].cdValLongDesc;
        this.statusSuggestion = this.filteredResults[0].cdValShrtDesc;
        this.selectedValue = this.filteredResults[0].cdValName;
        this.noResultsFound = false;
      }
      else if (this.filteredResults.length == 0) {
        this.noResultsFound = true;
        this.suggestionWindow = false;
        this.searchResultsWindow = false;
        this.openAccordion = false;
        this.selectedValue = "";
      }
      else {
        this.noResultsFound = false;
        this.suggestionWindow = false;
      }
    }
    else {
      this.noResultsFound = false;
      this.searchResultsWindow = false;
      this.suggestionWindow = false;
      this.openAccordion = false;
      this.filteredResults = [];
      this.selectedValue = "";
    }
  }
  /**
   * Method To get the description code from db and setting it up to accordion
   */
  showDescriptionandInventoryStatus() {
    this.selectedValue = this.rule.inventory_status ? this.rule.inventory_status : "";
    if (this.selectedValue != undefined && this.selectedValue != "") {
      var target = this.selectedValue.toLowerCase();;
      this.filteredResults = this.inventoryStatusDataset.filter(character => {
        return character.cdValName.toLowerCase().includes(target);
      });
      this.statusDescription = this.filteredResults[0]?.cdValLongDesc ? this.filteredResults[0]?.cdValLongDesc : "No Description Available for Selected Status";
      this.openAccordion = true;
    }
  }

  /**
   * populates the data on all forms
  */
  populateRuleDataOnForm(rule) {
    this.showForms = true;
    this.rule = rule;
    this.getConceptsClientsData();
    if (rule?.rule_metadata?.corpus_id) {
      delete rule.rule_metadata.corpus_id;
      this.qbQuery = JSON.parse(JSON.stringify(qbQueryDefault));
      this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[1];
      this.querySpecificationJson.find((item) => item.id === "sqlType").value = "qb";
      this.showQueryBuilderComponents = true;
    }
    else if (rule?.execution_type == "sql_query") {
      this.customSql = rule.conditions[0].query;
      this.querySpecificationJson.find((item) => item.id === "sqlType").value = "custSql";
      this.querySpecificationJson[1].groupControls.find((item) => item.id === "rulesLevel").selectedVal = rule.rule_level + " Level";
      this.customSqlJson.find((item) => item.id === "customSql").value = this.customSql;
      if (Object.keys(this.qbConfig.fields).length) {
        this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[1];
      }
      this.clientIdSelected = rule.client;
      this.clientIdForECP = rule.clientId;
      this.showQueryBuilderComponents = false;
    }
    else {
      this.qbQuery = this.modifyStructureToShowQB(rule?.conditions[0]);
      this.querySpecificationJson.find((item) => item.id === "sqlType").value = "qb";
      this.showQueryBuilderComponents = true;
    }
    this.rule.inventory_status = rule.inventory_status;
    this.getDependentDropdownsValues(rule.rule_type);
    this.getDependentDropdownsLtrType(rule.letter_type);
    this.getDependentDropdownsLtrSubType(rule.ltr_rule_sub_type);
    this.getDependentDropdownLtrOVPDuration(rule.number_of_reminder_letter)

    this.retroApply = rule.retro_apply;
    this.bypassApply = rule.bypass_apply;
    this.headerLevel = rule.header_level ? rule.header_level : false;
    this.showDescriptionandInventoryStatus();
    this.levelIndicator = rule.rule_level + " Level";
    if (!rule.letter_concept_type) {
      rule.letter_concept_type = 'Single';
    }
    this.relationSHJSON = [
      {
        type: 'group',
        name: 'rules',
        label: '',
        column: 1,
        groupControls: [
          {
            type: 'select',
            name: 'rule_type',
            label: 'Rule Type',
            options: this.ruleTypes,
            optionName: 'name',
            optionValue: 'value',
            column: 2,
            closeOnSelect: true,
            id: 'rule_type',
            relationship: this.dependentFieldsData,
            selectedVal: rule.rule_type,
            disabled: false,
            required: true,
            placeholder: 'Choose Rule Type'
          },
          {
            type: 'select',
            name: 'letter_type',
            label: 'Letter Type',
            column: 2,
            id: 'letter_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.letterType,
            relationOptions: [],
            relationship: this.dependentLetterData,
            selectedVal: rule.letter_type,
            required: this.showField('letter_type', rule.rule_type),
            disabled: false,
            visible: this.showField("letter_type", rule.rule_type)
          },
          {
            type: 'select',
            name: 'ltr_rule_sub_type',
            label: 'Rule Subtype',
            column: 2,
            id: 'ltr_rule_sub_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.ltrRuleSubTypes,
            relationOptions: [],
            relationship: this.dependentsubRuleData,
            selectedVal: rule.ltr_rule_sub_type,
            required: rule.rule_type == 'letters' ? this.showFieldLtrType("ltr_rule_sub_type", rule.letter_type) : this.showField("ltr_rule_sub_type", rule.rule_type),
            disabled: false,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrType("ltr_rule_sub_type", rule.letter_type) : this.showField("ltr_rule_sub_type", rule.rule_type)
          },
          {
            options: this.calculationFields,
            optionName: 'name',
            optionValue: 'value',
            label: 'Calculation Fields',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'calculation_fields',
            column: '2',
            disabled: false,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("calculation_fields", rule.ltr_rule_sub_type) : this.showField("calculation_fields", rule.rule_type),
            id: 'calculation_fields',
            selectedVal: this.isNull(rule.calculation_fields) ? "" : rule.calculation_fields[0],
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("calculation_fields", rule.ltr_rule_sub_type) : this.showField("calculation_fields", rule.rule_type),
            placeholder: 'Choose Calculation Field'
          },
          {
            type: 'select',
            name: 'rule_subtype',
            label: 'Rule Subtype',
            column: 2,
            id: 'rule_subtype',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.ruleSubTypes,
            relationOptions: [],
            disabled: false,
            visible: this.showField("rule_subtype", rule.rule_type),
            selectedVal: rule.rule_subtype,
            required: this.showField('rule_subtype', rule.rule_type),
            placeholder: 'Choose Rule Subtype'
          },
          {
            options: this.calculationFields,
            optionName: 'name',
            optionValue: 'value',
            label: 'Calculation Fields',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'calculation_fields',
            column: '2',
            disabled: false,
            visible: this.showField('calculation_fields', rule.rule_type),
            id: 'calculation_fields',
            selectedVal: this.isNull(rule.calculation_fields) ? "" : rule.calculation_fields[0],
            required: this.showField('calculation_fields', rule.rule_type),
            placeholder: 'Choose Calculation Field'
          },
          {
            options: this.reminderLtrCount,
            optionName: 'name',
            optionValue: 'id',
            label: 'Number Of Reminder Letters',
            type: 'select',
            closeOnSelect: true,
            name: 'number_of_reminder_letter',
            column: '2',
            id: 'number_of_reminder_letter',
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("number_of_reminder_letter", rule.ltr_rule_sub_type) : this.showField("number_of_reminder_letter", rule.rule_type),
            disabled: false,
            customTags: true,
            selectedVal: rule.number_of_reminder_letter,
            placeholder: 'Choose Reminder Letters',
            relationship: this.dependentsubRuleDurationData,
            relationOptions: [],
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("number_of_reminder_letter", rule.ltr_rule_sub_type) : this.showField("number_of_reminder_letter", rule.rule_type)
          },
          {
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 1(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_in_days',
            column: '2',
            id: 'letter_wait_duration_in_days',
            customTags: true,
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_wait_duration_in_days", rule.ltr_rule_sub_type) : this.showField("letter_wait_duration_in_days", rule.rule_type),
            disabled: false,
            options: this.ltrWaitDuration,
            relationOptions: [],
            selectedVal: rule.letter_wait_duration_in_days,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_wait_duration_in_days", rule.ltr_rule_sub_type) : this.showField("letter_wait_duration_in_days", rule.rule_type)
          },
          {
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 2(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_ovp_2',
            column: '2',
            id: 'letter_wait_duration_ovp_2',
            customTags: true,
            disabled: false,
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubTypeOVPReminder("letter_wait_duration_ovp_2", rule.number_of_reminder_letter) : this.showField("letter_wait_duration_ovp_2", rule.rule_type),
            options: this.ltrWaitDurationOvp2,
            relationOptions: [],
            selectedVal: Number(rule.letter_wait_duration_ovp_2),
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubTypeOVPReminder("letter_wait_duration_ovp_2", rule.number_of_reminder_letter) : this.showField("letter_wait_duration_ovp_2", rule.rule_type)
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Grace Period(in days)',
            type: 'numberSelect',
            closeOnSelect: true,
            name: 'grace_period_in_days',
            column: '2',
            disabled: false,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("grace_period_in_days", rule.ltr_rule_sub_type) : this.showField("grace_period_in_days", rule.rule_type),
            id: 'grace_period_in_days',
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("grace_period_in_days", rule.ltr_rule_sub_type) : this.showField("grace_period_in_days", rule.rule_type),
            customTags: true,
            minimum: 0,
            maximum: 100,
            value: rule.grace_period_in_days,
            placeholder: 'Choose Grace Period Min 0 Max 100'
          },
          {

            optionName: 'name',
            optionValue: 'value',
            label: 'Type of days',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'type_of_days',
            column: '2',
            id: 'type_of_days',
            options: this.typeOfdays,
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("type_of_days", rule.ltr_rule_sub_type) : this.showField("type_of_days", rule.rule_type),
            disabled: false,
            selectedVal: rule.type_of_days,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("type_of_days", rule.ltr_rule_sub_type) : this.showField("type_of_days", rule.rule_type),
          },
          {
            options: this.lookBackPeriodValues,
            optionName: 'name',
            optionValue: 'value',
            label: 'Lookback Period (in months)',
            type: 'select',
            closeOnSelect: true,
            name: 'lookup_dates',
            column: '2',
            disabled: false,
            visible: this.showField('lookup_dates', rule.rule_type),
            id: 'lookup_dates',
            selectedVal: rule.lookup_dates?.value ? Number(rule.lookup_dates.value) : '',
            required: this.showField('lookup_dates', rule.rule_type),
            placeholder: 'Choose Lookback Period'
          },
          {
            options: this.laggingPeriodValues,
            optionName: 'name',
            optionValue: 'value',
            label: 'Lagging Period (in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'lagging_period',
            column: '2',
            disabled: false,
            visible: this.showField('lagging_period', rule.rule_type),
            id: 'lagging_period',
            required: this.showField('lagging_period', rule.rule_type),
            selectedVal: rule.lagging_period ? Number(rule.lagging_period.value) : '',
            placeholder: 'Choose Lagging Period'
          },
          {
            options: [
              { name: this.selectedProfileClientName, id: this.selectedProfileClientId }
            ],
            optionName: 'name',
            optionValue: 'name',
            label: 'Client Name',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'client_name',
            column: '2',
            disabled: false,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("client_name", rule.ltr_rule_sub_type) : this.showField("client_name", rule.rule_type),
            id: 'client_name',
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("client_name", rule.ltr_rule_sub_type) : this.showField("client_name", rule.rule_type),
            selectedVal: rule.client_name,
            placeholder: 'Choose Client Name'
          },
          {
            optionName: 'name',
            optionValue: 'value',
            label: 'Provider',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'provider',
            column: '2',
            disabled: false,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("provider", rule.ltr_rule_sub_type) : this.showField("provider", rule.rule_type),
            id: 'provider',
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("provider", rule.ltr_rule_sub_type) : this.showField("provider", rule.rule_type),
            options: this.provider,
            selectedVal: rule.provider,
          },
          {

            optionName: 'name',
            optionValue: 'value',
            label: 'Concept',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'letter_concept_type',
            column: '2',
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_concept_type", rule.ltr_rule_sub_type) : this.showField("letter_concept_type", rule.rule_type),
            disabled: false,
            options: this.letterConcepts,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_concept_type", rule.ltr_rule_sub_type) : this.showField("letter_concept_type", rule.rule_type),
            id: 'letter_concept_type',
            selectedVal: rule.letter_concept_type,

          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Max no Of Claims Per Letter',
            type: 'numberSelect',
            closeOnSelect: true,
            name: 'max_no_of_claims_per_letter',
            column: '2',
            disabled: false,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("max_no_of_claims_per_letter", rule.ltr_rule_sub_type) : this.showField("max_no_of_claims_per_letter", rule.rule_type),
            id: 'max_no_of_claims_per_letter',
            required: rule.rule_type == 'letters' ? this.showFieldLtrSubType("max_no_of_claims_per_letter", rule.ltr_rule_sub_type) : this.showField("max_no_of_claims_per_letter", rule.rule_type),
            customTags: true,
            minimum: 1,
            maximum: 15,
            value: rule.max_no_of_claims_per_letter,
            placeholder: 'Choose Max No Of Claims Per Letter Min 1 Max 15'
          },
          {
            options: [
              {
                name: 'Audit',
                value: 'audit',
              },
              {
                name: 'Provider Audit',
                value: 'providerAudit',
              },
              {
                name: 'Report Audit',
                value: 'reportAudit',
              },
              {
                name: 'Over Payment',
                value: 'overPayment',
              },
              {
                name: 'Business Audit',
                value: 'businessAudit',
              },
            ],
            optionName: 'name',
            optionValue: 'value',
            label: 'Inventory Type',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'audit_type',
            column: '2',
            disabled: false,
            visible: this.showField('audit_type', rule.rule_type),
            id: 'audit_type',
            required: this.showField('audit_type', rule.rule_type),
            selectedVal: rule.audit_type,
            placeholder: 'Choose Inventory Type'
          },
          {
            label: 'Release By',
            type: 'date',
            name: 'release_by',
            id: 'release_by',
            column: '2',
            disabled: false,
            visible: this.showField('release_by', rule.rule_type),
            pickerType: 'single',
            required: this.showField('release_by', rule.rule_type),
            dateFormat: 'MM-DD-YYYY',
            minDate: (new Date(rule.release_by) > new Date()) ? Date.now() : this.dateService.getDbgDateFormat(rule.release_by),
            value: this.dateService.getDbgDateFormat(rule.release_by),
            placeholder: 'Choose Date'
          },
        ],
      },

    ];

    this.generalDetailsJson = [
      {
        type: 'group',
        name: 'generalDetailsLeft',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Rule Name',
            type: 'text',
            name: 'rule_name',
            column: '1',
            disabled: false,
            value: rule.rule_name,
            required: true,
            placeholder: 'Enter Rule Name'
          },
          {
            label: 'Rule Description',
            type: 'textarea',
            name: 'description',
            id: 'description',
            column: '1',
            disabled: false,
            value: rule.description,
            required: true,
            placeholder: 'Enter Description'
          },
          {
            label: 'Term Reason',
            type: 'text',
            name: 'term_reason',
            column: '1',
            disabled: false,
            value: rule.term_reason,
            placeholder: 'Enter Term Reason'
          },
        ],
      },
      {
        type: 'group',
        name: 'generalDetailsRight',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Start Date',
            type: 'date',
            name: 'start_date',
            id: 'start_date',
            column: '3',
            disabled: false,
            value: this.dateService.getDbgDateFormat(rule.start_date),
            pickerType: 'single',
            required: true,
            dateFormat: 'MM-DD-YYYY',
            placeholder: 'Enter Date',
            relatedDateControls: [{
              target: 'end_date'
            }]
          },
          {
            label: 'End Date',
            type: 'date',
            name: 'end_date',
            id: 'end_date',
            column: '3',
            disabled: false,
            pickerType: 'single',
            value: this.dateService.getDbgDateFormat(rule.end_date),
            dateFormat: 'MM-DD-YYYY',
            minDate: this.dateService.getFutureDate(rule.start_date, 1, 'MM-dd-YYYY'),
            required: false,
            placeholder: 'Enter Date'
          },
          {
            label: 'Status',
            type: 'text',
            name: 'status',
            column: '3',
            disabled: true,
            value: rule.status ? 'Active' : 'Inactive',
          },
          {
            label: 'Review Reminder Date',
            type: 'date',
            name: 'review_remainder_date',
            column: '1',
            disabled: false,
            pickerType: 'single',
            value: rule.review_remainder_date,
            dateFormat: 'MM-DD-YYYY',
            required: true,
            placeholder: 'Enter Date'
          },
          {
            label: 'Business Owner',
            group: '',
            type: 'text',
            name: 'business_owner',
            column: '2',
            groupColumn: '1',
            disabled: false,
            required: true,
            maxLength: 50,
            placeholder: 'Please Enter Business Owner'
          },
        ],
      },
    ];
    this.additionalDetailsJson = [
      {
        type: 'group',
        name: 'additionalDetailsTop',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'External Point Of Contact',
            group: '',
            type: 'text',
            name: 'external_point_of_contact',
            id: 'external_point_of_contact',
            column: '2',
            groupColumn: '1',
            disabled: false,
            value: rule.external_point_of_contact,
            placeholder: 'Enter External Point Of Contact'
          },
        ],
      },
      {
        type: 'group',
        name: 'additionalDetailsBottom',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'Created By',
            type: 'text',
            name: 'created_by',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: "",
          },
          {
            label: 'Created Date',
            type: 'date',
            name: 'created_ts',
            id: 'created_ts',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: "",
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
          {
            label: 'Updated By',
            type: 'text',
            name: 'updated_by',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: "",
          },
          {
            label: 'Updated Date',
            type: 'date',
            name: 'updated_ts',
            id: 'updated_ts',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: "",
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
        ],
      },
    ];
  }

  /**
   * populates objects with all dependent dropdown values
  */
  getDependentDropdownsValues(conditionKey) {
    this.dependentFieldsData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {

            case 'rule_subtype':
              this.ruleSubTypes = dependentField.dataset;
              break;
            case 'letter_type':
              this.letterType = dependentField.dataset;
              break;
            case 'calculation_fields':
              this.calculationFields = dependentField.dataset;
              break;
            case 'lookup_dates':
              this.lookBackPeriodValues = dependentField.dataset;
              this.lookBackPeriodValues.forEach(fieldValue => {
                fieldValue.value = fieldValue.value;
              });
              break;
            case 'lagging_period':
              this.laggingPeriodValues = dependentField.dataset;
              this.laggingPeriodValues.forEach(fieldValue => {
                fieldValue.value = fieldValue.value;
              });
              break;
          }
        });
      }
    });
  }


  getDependentDropdownsLtrType(conditionKey) {
    this.dependentLetterData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'ltr_rule_sub_type':
              this.ltrRuleSubTypes = dependentField.dataset;
              break;
          }
        });
      }
    });
  }

  getDependentDropdownsLtrSubType(conditionKey) {
    this.dependentsubRuleData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          console.log(dependentField)
          switch (dependentField.id) {
            case 'letter_wait_duration_in_days':
              this.ltrWaitDuration = dependentField.dataset;
              break;
            case 'number_of_reminder_letter':
              this.reminderLtrCount = dependentField.dataset;
              console.log(console.log(dependentField))
              break;
            case 'type_of_days':
              this.typeOfdays = dependentField.dataset;
              break;
            case 'calculation_fields':
              this.calculationFields = dependentField.dataset;
              break;
            case 'provider':
              this.provider = dependentField.dataset;
              break;
            case 'letter_concept_type':
              this.letterConcepts = dependentField.dataset;
              break;
          }
        });
      }
    });
  }
  getDependentDropdownLtrOVPDuration(conditionKey) {
    this.dependentsubRuleDurationData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'letter_wait_duration_ovp_2':
              this.ltrWaitDurationOvp2 = dependentField.dataset;
              break;
          }
        });
      }
    });
  }

  /**
   * based on ruletype value, some fields will be shown/hidden
   * that is evaluated here
  */
  showField(field, conditionKey) {
    let makeItVisible = true;
    this.dependentFieldsData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });


      }
    });
    return makeItVisible;
  }

  showFieldLtrType(field, conditionKey) {
    let makeItVisible = true;

    this.dependentLetterData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }

  showFieldLtrSubType(field, conditionKey) {
    let makeItVisible = true;

    this.dependentsubRuleData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }
  showFieldLtrSubTypeOVPReminder(field, conditionKey) {
    let makeItVisible = true;
    this.dependentsubRuleDurationData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }

  /**
   * modifies selected query builder criterias to the format query builder understands
  */
  modifyStructureToShowQB(qbQuery) {
    const operatorMap = operatorsMapToShowInQb;
    let customFields = [];
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case 'log':
          this.condition = v;
          break;
        case 'conditions':
          this.rules = v;
          break;
        case 'lval':
          this.field = v;
          break;
        case 'rval':
          this.value = v;
          this.startValue = v.start;
          this.endValue = v.end;
          customFields.push(v);
          break;
        case 'op':
          this.operator = operatorMap[v];
          break;
        case 'config':
        case 'operatorList':
        case 'delete':
        case 'json_path':
          delete qbQuery[k];
          break;
        default:
          return v;
      }
    });
    this.pushCustomFieldsToQBConfig(customFields);
    return parsed;
  }

  /**
   * checks for undefined value
  */
  isDefined(fieldValue) {
    if (fieldValue != undefined) return true;
    else return false;
  }
  /**
  * takes to frequently added criteria screen
 */
  AddNewCriteriaOnClick(): void {
    this.router.navigate([
      'product-catalog/rules/create-frequently-used-criteria',
    ]);
  }
  /**
  * takes to dashboard
 */
  returnHomeClick(): void {
    this.router.navigate(['product-catalog/rules']);
  }
  /**
  * populates updated by and updated date
  * method gets called after file upload
  */
  populateAdditionalDetails(updateData) {
    this.additionalDetailsJson = [
      {
        type: 'group',
        name: 'additionalDetailsTop',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'External point of contact',
            group: '',
            type: 'text',
            name: 'external_point_of_contact',
            id: 'external_point_of_contact',
            column: '2',
            groupColumn: '1',
            disabled: false,
            value: this.rule.external_point_of_contact,
          },
        ],
      },
      {
        type: 'group',
        name: 'additionalDetailsBottom',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'Created by',
            type: 'text',
            name: 'created_by',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: this.rule.created_by,
          },
          {
            label: 'Created date',
            type: 'date',
            name: 'created_ts',
            id: 'created_ts',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: this.dateService.getDbgDateFormat(this.rule.created_ts),
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
          {
            label: 'Updated by',
            type: 'text',
            name: 'updated_by',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: updateData.attached_by,
          },
          {
            label: 'Updated date',
            type: 'date',
            name: 'updated_ts',
            id: 'updated_ts',
            column: '2',
            groupColumn: '1',
            disabled: true,
            value: this.dateService.getDbgDateFormat(updateData['saved date']),
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
        ],
      },
    ];
  }

  /**
   * Method will be called on click of download button 
   * and calls service API to get the file
   */
  DownloadMultiCriteriaFile() {
    this.showLoader = true;
    this.RulesApiService.getMultipleCriteriaFile(this.ruleId, this.corpusId).subscribe(data => {
      this.showLoader = false;
      this.generateExceldata(data, "multi_criteria_file");
    },
      error => {
        this.showLoader = false;
        this.alertService.setErrorNotification({
          notificationHeader: FAIL,
          notificationBody: `${error.statusText}`,
        });
      });
  }

  /**
   * Invoked when excel export icon is clicked
   * @function generateExceldata generates a excel file
   * @param data response from API
   * @param fileName fileName
   * @returns excelFile
   */
  generateExceldata(data: any, fileName: any) {
    if (Object.keys(data).length) {
      let a = document.createElement("a");
      a.id = "excel";
      document.body.appendChild(a);
      a.href = URL.createObjectURL(data.body);
      a.download = fileName;
      a.click();
    }
  }

  /**
   * Method pushes cusom fields to query Builder config
   */

  pushCustomFieldsToQBConfig(customFields): void {
    this.qbConfig.customFieldList = {};
    this.qbConfig.customFieldList.dataset = [];
    customFields.forEach(column => {
      this.qbConfig.customFieldList.dataset.push({ "name": column, "id": column, "collection": WORKSHEET_HEADERS });
    });
  }

  /**
   * method called on the popup close
   */
  createClosePopup = () => {
    this.createOpenPopup = false;
  }

  closeFileUploadModal(): void {
    this.openFileUploadConfirmModal = false;
  }

  /**
   * Closes confirmation modal
  */
  closeConfirmationModal() {
    this.showSegmentedControl = false;
    this.openConfirmationModal = false;
    this.sgDashboardDataset.forEach(element => {
      element.checked = false;
    });
    this.sgDashboardDataset[this.unSelectedIndex].checked = true;
    if (this.unSelectedIndex == 0) {
      this.isStandardQBSelected = true;
      this.showSubmit = true;
    }
    else {
      this.isStandardQBSelected = false;
      this.showSubmit = false;
    }
    setTimeout(() => this.showSegmentedControl = true, 50);
  }

  /**
   * Method to show only roles based on client site
   */
  onBussinessOwnerChange(event: any) {
    this.ruleSubmitButton = true;
    this.isFormSubmitted = false
    let bussinessOwnerName = event.current.generalDetailsRight.business_owner;
    bussinessOwnerName ? this.userManagementSvc.checkManagerNameValidation(bussinessOwnerName) : '';
    if (this.userManagementSvc.validationOfName) {
      this.isFormSubmitted = true
      this.generalDetailsJson[1].groupControls[4].customErrMsg = RegistrationConstants.INVALID_CHAR
      this.ruleSubmitButton = false;
    }
  }

  /**
     * Clears QueryBuilder and closes the confirmation popup
    */
  clearQB() {
    this.enableQueryBuilder();
    this.showQBuilder = false;
    delete this.qbConfig.customFieldList;
    if (Object.keys(this.qbConfig.fields).length) {
      this.qbQuery = JSON.parse(JSON.stringify(qbQueryDefault));
      this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[2];
    }
    if (this.unSelectedIndex == 1) {
      this.isStandardQBSelected = true;
      this.showSubmit = true;
      this.corpusId = "";
      this.multiCriteriaFile = {};
    }
    else {
      this.isStandardQBSelected = false;
      this.showSubmit = false;
    }
    setTimeout(() => this.showQBuilder = true, 50);
    this.openConfirmationModal = false;
  }

  /**
     * method checks for client name changes and push it to query builder
    */
  validateValueChanges(event) {
    this.showQBuilder = false;
    let changedRuleTypeValue = event.current.rules.rule_type;
    let oldRuleTypeValue = event.previous.rules.rule_type;
    if (changedRuleTypeValue == 'lag' && oldRuleTypeValue != changedRuleTypeValue) {
      this.clientIdQBConfigEntryBackUp = this.qbConfig.fields.CLNT_ID;
      delete this.qbConfig.fields.CLNT_ID;
    }
    else if (oldRuleTypeValue == 'lag' && oldRuleTypeValue != changedRuleTypeValue) {
      this.showQBuilder = false;
      this.qbConfig.fields['CLNT_ID'] = this.clientIdQBConfigEntryBackUp;
    }
    else if (changedRuleTypeValue == 'letters') {
      this.suggestionWindow = false;
      this.enableInventoryStatus = false;
      this.showQBuilder = false;
      this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[1];
      if (this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != null && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidation' && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidations') {
        this.enableInventoryStatus = true;
      }
    }
    setTimeout(() => this.showQBuilder = true, 10);
  }

  /* 
    * method evaluating client/concept ids from the event
    * triggered by querybuilder field value change
    */
  getClientConceptValue(event) {
    let fieldChanged = event.rule;
    let fieldValue = event.event.name;
    let fieldValueId = event.event.id;
    if (fieldChanged.field == "CLNT_ID") {
      this.clientIdSelected = fieldValue;
      this.clientIdForECP = fieldValueId;
      this.conceptIdSelected = [];
    } else if (fieldChanged.field == "CNCPT_ID") {
      this.conceptIdSelected = fieldChanged.value;
      this.clientIdSelected = "";
    }
  }

  /**
     * method sets rule level and get called when rule type changes and 
     * query builder criteria changes
    */

  /**
  * Method fires on segemented selection
  * @param event 
  */
  _onDashboardSGSelection(event: any): void {
    switch (event.selection.label) {
      case STANDARD:
        this.unSelectedIndex = 1;
        this.openConfirmationModal = true;
        this.removeCloseButton();
        break;
      case UPLOAD_FILE:
        setTimeout(() => {
          this.unSelectedIndex = 0;
          this.openConfirmationModal = true;
          this.removeCloseButton();
        }, 50);
        break;
      default:
        break;
    }
  }

  removeCloseButton(): void {
    setTimeout(() => {
      const elements = document.querySelectorAll('marketplace-popup .modal-header .close');
      elements.forEach(element => {
        const htmlEle = element as HTMLElement;
        htmlEle.style.display = 'none';
      });
    }, 0);
  }

  /**
     * Method triggers on file selection
     * @param event 
     */
  uploadMultiCriteriaFile(event: Event): void {
    this.showQBuilder = false;
    if ((Array.isArray(event) && event.length == 0) || event['changed'] == "") {
      this.disableUploadBtn = true;
    }
    else {
      this.disableUploadBtn = false;
    }
    delete this.qbConfig.customFieldList;
    this.showSubmit = false;
    this.multiCriteriaFile = event;
    setTimeout(() => {
      this.showQBuilder = true;
      this.enableQueryBuilder();
    }, 50);
  }

  /**
 * Method invoked when File Parser completes loading the table
 * @param event 
 */
  onParseComplete(event) {
    this.showQBuilder = false;
    this.qbConfig.customFieldList = {};
    this.qbConfig.customFieldList.dataset = [];
    let parserDataset = event?.sheet;
    parserDataset.forEach(element => {
      Object.keys(element.dataJSON[0]).forEach(column => {
        if (column != "")
          this.qbConfig.customFieldList.dataset.push({ "name": column, "id": column, "collection": "Worksheet Headers" });
      });
    });
    this.showQBuilder = true;
  }

  /**
     * Which closes the current screen and show dashboard
    */
  cancelCreate() {
    this.router.navigate([`${this.breadcrumbDataset[1].url}`]);
  }

  /**
     * Setting up the modal with visibility flags
    */
  uploadFileInCreateRule() {
    this.createUploadOpenPopup = true;
    this.isFileReady = true;
    this.isTextReady = true;
    this.fileUploadPopup = "block";
  }

  /**
     * closing up the modal with visibility flags
    */
  createUploadClosePopup() {
    this.createUploadOpenPopup = false;
    this.router.navigate([`${this.breadcrumbDataset[1].url}`]);
  }

  /*
  * recursive function to check empty field in querybuilder
  */
  recursiveFuncForCheckingEmptyField(event) {
    this.qbFilled = true;
    for (let i = 0; i < event.length; i++) {
      if (event[i].value == "" || event[i].value == constants.SELECT) {
        this.qbFilled = false;
        return;
      } else if (event[i].rules) {
        this.recursiveFuncForCheckingEmptyField(event[i].rules);
      }
    }
  }

  /**
     * Method will be called to send file and Query builder mapping to be saved 
     */
  multipleCriteriaFileUpload(): void {
    this.recursiveFuncForCheckingEmptyField(this.qbQuery[constants.RULES]);

    if (!this.qbFilled) {
      this.uploadFileStatus = constants.ATTENTION;
      this.uploadFileStatusMsg = constants.FILL_QB_STATUS_MESSAGE;
      this.openFileUploadConfirmModal = true;
    }
    if (this.qbFilled) {
      const formData = new FormData();
      let conditions = [];
      conditions.push(this.modifyQBuilderStructure(this.qbQuery));
      Object.keys(this.multiCriteriaFile).forEach(key => {
        formData.append("file", this.multiCriteriaFile[key]);
      });
      formData.append("conditions", JSON.stringify(conditions));
      this.showLoader = true;
      this.RulesApiService.uploadFileAndQBCriteria(formData, this.levelIndicator)
        .subscribe(
          data => {
            if (data) {
              this.showSubmit = true;
              this.corpusId = data?.result?.uploaded_files[0]?.corpus_id;
              this.showLoader = false;
              this.uploadFileStatus = SUCCESS;
              this.uploadFileStatusMsg = FILE_UPLOAD_MSG;
              this.openFileUploadConfirmModal = true;
              this.removeFileParserTable();
            }
          },
          error => {
            this.showLoader = false;
            this.uploadFileStatus = FAIL;
            this.uploadFileStatusMsg = error.statusText;
            this.openFileUploadConfirmModal = true;
          });
    }
  }

  /**
   * Method to remove pointer function none from QB
   */
  enableQueryBuilder() {
    let queryBuilderDivElement = document.querySelector('div.enabledQb');
    queryBuilderDivElement?.classList?.remove('pointerFuncNone');
  }

  /**
   * Remove table from the file parser micro front end
   */
  removeFileParserTable(): void {
    let tableDivElement = document.querySelector('div.sheetsData-container');
    let queryBuilderDivElement = document.querySelector('div.enabledQb');
    queryBuilderDivElement.classList.add('pointerFuncNone');
    tableDivElement.remove();
  }

  /**
   * Get all json file data and assign it to respective objevts
   */
  getAllJsonFilesData(): void {
    this.RulesApiService.getAssetsJson(constants.RULE_QUERY_SPEC_JSON).subscribe((data) => {
      this.querySpecificationJson = data.sqlStructure;
      this.querySpecificationJson[0].value = this.showQueryBuilderComponents ? "qb" : "custSql";
      this.customSqlJson = data.customSQL;
      this.showQuerySpec = true;
    });
  }

  /**
   * Get Concepts and client data
   */
  getConceptsClientsData(): void {
    this.showLoader = true;
    let tokenVal = localStorage?.getItem(constants.TOKEN);
    let _clientData = this.clientApiService.getAllClientsInPreferenceCenter();
    let _conceptData = this.conceptApiService.getProductConceptsId(tokenVal);

    forkJoin([_clientData, _conceptData]).subscribe(([clientData, conceptData]) => {
      if (clientData) {
        this.clientData = clientData.map(x => ({ value: x.clientId, name: x.clientName }));
      }

      if (conceptData) {
        this.conceptData = conceptData.executionConceptAnalyticResponse
          .filter(x => x.clientId === this.selectedProfileClientId || (this.selectedProfileClientId === ANTM_ID && x.clientId === 0))
          .map(x => ({ id: x.exConceptReferenceNumber, name: x.exConceptReferenceNumber }));
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0].options = this.conceptData;
      }
      if (this.rule.concept.length >= 1) {
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'rulesLevel')[0].selectedVal = "Concept Level";
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'conceptId')[0].selectedVal = this.rule.concept;
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'conceptId')[0].visible = true;
      } else if (this.rule.clientId) {
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'rulesLevel')[0].selectedVal = "Client Level";
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'clientId')[0].selectedVal = this.rule.clientId;
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'clientId')[0].visible = true;
      } else {
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].selectedVal = constants.GLOBAL_LEVEL;
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == 'conceptId')[0].visible = false
      }
      const selectedRuleLevel = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].selectedVal;
      if (this.selectedProfileClientId === 59 && selectedRuleLevel !== constants.GLOBAL_LEVEL) {
        this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.PRODUCT)[0].visible = true;
      }
      this.showLoader = false;

    }, error => {
      this.alertService.setErrorNotification({
        notificationHeader: "Error",
        notificationBody: 'Failed to Load Concepts Data ',
      });
      this.clientData = [];
      this.showLoader = false;
    });
  }

  /**
    * triggers when chip is closed
   */
  closeStateChip(selectedState) {
    this.showQuerySpec = false;
    this.conceptIdSelectedForChips = this.conceptIdSelectedForChips.filter(x => x !== selectedState);
    this.conceptIdSelected = this.conceptIdSelectedForChips;
    this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0].selectedVal = this.conceptIdSelected;
    setTimeout(() => {
      this.showQuerySpec = true;
    }, 0);
  }

  /**
   * Set client or concept at rule level defination
   */
  setClientConcept() {
    if (!this.showQueryBuilderComponents) {
      if (this.rule.clientId) {
        this.querySpecificationJson[1]['groupControls'][0].selectedVal = constants.CLIENT_LEVEL;
        this.querySpecificationJson[1]['groupControls'][1].options = [{ name: this.rule.client, value: this.rule.clientId }];
        this.querySpecificationJson[1]['groupControls'][1].selectedVal = this.rule.clientId;
        this.querySpecificationJson[1]['groupControls'][1].visible = true;
      }
      else if (this.rule.concept) {
        this.querySpecificationJson[1]['groupControls'][0].selectedVal = constants.CONCEPT_LEVEL;
        this.querySpecificationJson[1]['groupControls'][2].visible = true;
        this.querySpecificationJson[1]['groupControls'][2].value = this.rule.concept;
      }
    }
  }

  /**
   * triggers on change of query spec dynamic form
  */
  mapValuesFromQuerySpecToJson(event): void {
    this.querySpecDetailsResponse = event.current;
    this.conceptIdSelected = [];
    let ruleLevel = event.current.group.rulesLevel;
    this.isRuleLevelPresent = ruleLevel == undefined ? false : true;

    switch (ruleLevel) {
      case "Concept Level":
        this.levelIndicator = constants.CONCEPT_LEVEL;
        this.isRuleLevelPresent = true;
        this.conceptIdSelected = event.current.group?.conceptId;
        this.conceptIdSelectedForChips = [...this.conceptIdSelected?.map((c) => c)];
        this.conceptIdSelected = this.conceptIdSelectedForChips;
        this.clientIdForECP = null;
        this.clientIdSelected = null;
        this.querySpecificationJson[1].groupControls.find(c => c.name == "clientId").selectedVal = undefined;
        break;
      case "Global Level":
        this.levelIndicator = constants.GLOBAL_LEVEL;
        this.isRuleLevelPresent = true;
        this.clientIdForECP = null;
        this.clientIdSelected = null;
        this.conceptIdSelected = [];
        this.conceptIdSelectedForChips = [];
        this.querySpecificationJson[1].groupControls.find(c => c.name == "conceptId").selectedVal = undefined;
        this.querySpecificationJson[1].groupControls.find(c => c.name == "clientId").selectedVal = undefined;
        break;
      case "Client Level":
        this.levelIndicator = constants.CLIENT_LEVEL;
        this.isRuleLevelPresent = true;
        this.clientIdSelected = this.clientData.find((clientObj) =>
          clientObj.value == event.current.group?.clientId
        )?.name
        this.querySpecificationJson[1].groupControls.find(c => c.name == "conceptId").selectedVal = undefined;
        this.clientIdForECP = event.current.group?.clientId;
        this.conceptIdSelectedForChips = [];
        this.conceptIdSelected = [];
        break;

      default:
        this.conceptIdSelectedForChips = [];
        break;
    }
    this.querySpecDetailsFormEvent = event;
    if (this.querySpecDetailsResponse.sqlType == constants.QUERY_BUILDER) {
      this.showQueryBuilderComponents = true;
    }
    else {
      this.levelIndicator = constants.GLOBAL_LEVEL;
      this.showQueryBuilderComponents = false;
    }
    this._onRuleLevelChange(event);
  }

  /**
     * Method will be called on custom SQL change
     */
  _onRuleLevelChange(event): void {
    this.levelIndicator = event.current.group.rulesLevel;
    let conceptFrmMaster = this.masterDataFromAPI['query_fields']?.find(c => c.value == 'CLNT_ID')?.options.find((item) => item.id === this.selectedProfileClientId);
    conceptFrmMaster = [conceptFrmMaster]?.map(data => {
      return {
        "name": data.name,
        "value": data.id
      }
    });

    if (event.current.group.rulesLevel == constants.CLIENT_LEVEL && event.current.group.clientId) {
      this.clientIdSelected = this.queryBuilderClientDataset?.find(c => c.id == event.current.group?.clientId)?.name ?? conceptFrmMaster[0]?.name;
      this.clientIdForECP = event.current.group.clientId;
      this.conceptIdSelected = [];
    } else if (event.current.group.rulesLevel == constants.CONCEPT_LEVEL && event.current.group.conceptId) {
      this.conceptIdSelected = event.current.group.conceptId;
      this.clientIdSelected = "";
    }


    if (event.current.group.rulesLevel == constants.CLIENT_LEVEL && !event.current.group.clientId) {
      this.querySpecificationJson[1].groupControls.find(c => c.name == "clientId").options = this.queryBuilderClientDataset?.map(data => {
        return {
          "name": data.name,
          "value": data.id
        }
      }) ?? conceptFrmMaster;
    }
  }

  ruleLevelChange(event) {
    this.ruleLevelFormEvent = event;
  }

  /**
 * Method will be called on custom SQL change
 */
  _onSqlChange(event): void {
    this.customSql = event.current.customSql;
  }

  dropRecentList(event) { }

  qbFieldChange(event) { }

  qbChange(event) { }

  /**
   * Close save successful popup
   */
  savedConfirmPopupClose() {
    this.openImpactReportPopup = false;
  }

  /**
  * Navigating back to rules engine screen on click of cancel popup
 */
  cancelGenerateView() {
    this.fileUploadpopUpReset();
  }

  /**
 * Navigates to impact report screen of rule's effect on past executions.
 */
  generatePreview() {
    this.router.navigate([`/product-catalog/rules/impact-report/${this.updatedRuleId}`])
    this.openImpactReportPopup = false;
  }
}
