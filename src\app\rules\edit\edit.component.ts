import { Component, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { forkJoin } from 'rxjs';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { ROUTING_LABELS } from '../../_constants/menu.constant';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { RulesApiService } from '../_services/rules-api.service';
import { OperatorsMapForQb, operatorsMapToShowInQb, OperatorsRulesQB } from '../_services/Rules-QB-Constants';
import { UtilitiesService } from '../../_services/utilities.service';
import { constants } from '../rules-constants';

import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { INSIGHT_CLAIM_CONSTANTS } from 'src/app/_constants/insight-claim-constanst';
import { AuthService } from 'src/app/_services/authentication.services';
import { ToastService } from 'src/app/_services/toast.service';
import { DashboardComponent } from '../dashboard/dashboard.component';

const FAIL = "Fail";
const WORKSHEET_HEADERS = "Worksheet Headers";
const STANDARD = "Standard";
const UPLOAD_FILE = "Upload File";
const ANTM_ID = 59;
const FILE_UPLOAD_MSG = "File successfully uploaded. You may proceed with the rule creation.";
const SUCCESS = "Success";
const qbQueryDefault = {
  condition: 'and',
  rules: [
    {
      field: '',
      operator: 'Equal',
      value: '',
      static: true,
      active: true,
    }
  ]
}

@Component({
  selector: 'app-settings-rule-edit',
  templateUrl: './edit.component.html',
  styleUrls: ['./edit.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class EditComponent {
  ruleEditUploadRedraw: any;
  ruleId: number;
  public headerText: string = '';
  inventoryStatusDataset: any = [];
  showMaxLimitMsg: boolean = false;
  enableInventoryStatus: boolean = true;
  noResultsFound: boolean = false;
  letterType: any = [];
  ltrRuleSubTypes: any = [];
  ltrWaitDuration: any = [];
  reminderLtrCount: any = [];
  gracePeriod: any = [];
  typeOfdays: any = [];
  provider: any = [];
  ruleIdForGeneratePreview: string;
  concept: any = [];
  letterConcepts: any = [];
  isEditMainToJsonCount = 0;
  compatibleJsonForConcepts: any = [];
  orgnlCnptCarriedfrmLstEdit: any = [];
  addedConcepts: any = [];
  deletedConcepts: any = [];
  createdBy: string;
  isConceptDataReady: boolean = true;
  conceptsChangedPopUp: boolean = false;
  conceptsChanged: boolean = false;
  UsersScreenAccessList: any;
  product: string;
  productsList: { name: string; value: string; }[];
  isDataReceivedFromSubcription: boolean = false;
  isRetroEnabled: boolean = true;
  isBypassEnabled: boolean = true;
  reInstateRuleVersionDetails: any;
  selectedTabIndex: Number = 0;
  versionSeq: Number;
  isRuleDef: boolean;

  constructor(
    private clientApiService: ClientApiService,
    private conceptApiService: ProductApiService,
    private router: Router,
    private route: ActivatedRoute,
    private RulesApiService: RulesApiService,
    private dateService: UtilitiesService,
    private alertService: ToastService,
    private cookieService: CookieService,
    private userManagementSvc: UserManagementApiService,
    private authService: AuthService
  ) {
    this.getInventoryStatusData();
    this.userId = sessionStorage.getItem(ROUTING_LABELS.USER_ID)?.toUpperCase();
  }
  breadcrumbDataset = [
    { label: 'Home', url: '/' },
    { label: 'Rules engine', url: '/rules' },
    { label: 'Edit rule' },
  ];
  public kebabOptions: any = [
    { label: '<i class="fa fa-eye" aria-hidden="true"></i> XXX ', id: 'view' },
    { label: '<i class="fa fa-eye" aria-hidden="true"></i> XXX ', id: 'edit' },
    {
      label: '<i class="fa fa-eye" aria-hidden="true"></i> XXX ',
      id: 'delete',
    },
  ];
  isFileReady: boolean = false;
  isTextReady: boolean = false;
  isRuleLevelPresent: boolean = false;
  checkMandatoryMain: boolean = false;
  dataJSON: any;
  modalColumnConfigDuplicate: any = "./assets/json/duplicateRules.json";
  public labelName: string = "Status*";
  public inputname: string = "inventory-status";
  filteredResults: any = [];
  groupIcon: string = '<i class="fa fa-search" aria-hidden="true"></i>'
  public selectedValue: string = "";
  public statusDescription: string = "No Status Code Selected";
  public statusSuggestion: string = "";
  public statusInfo: string = "";
  scrollItems: any = 'none';
  openAccordion: boolean = false;
  ltrWaitDurationOVP: any;
  mainFieldsNullCheckCount = 0
  searchResultsWindow: boolean = false;
  public clientData: any = [];
  public conceptData: any = [];
  conceptIdSelected: any = [];
  clientIdSelected: number;
  selectedProfileClientId: number;
  selectedProfileClientName: string = "";
  setStatusOfRuleLevel: boolean;
  clientIdForECP: any;
  suggestionWindow: boolean = false;
  columnConfigDuplicatePopup: any;
  public fileUploadEditJSON: any;
  public isDisabled: any = true;
  duplicateRuleTableJson: any = [];
  public isLoading: any = false;
  public isDraftRule: boolean = false;
  public isEditedRule: boolean = false;
  public isDraftSubmission: boolean = false;
  public isInputDisabled: boolean = true;
  public postUploadDataJson: any;
  public tableRedraw: any;
  public isPriviousRedirectPage = true;
  levelIndicator: string = '';
  fileUploadType: string = 'multiple';
  fileUploadLabelText: string = 'Upload File';
  fileAccept: string = '.png,.xlsx,.pdf,.jpeg,.PNG,.XLSX,.PDF,.JPEG';
  fileEnable: boolean = true;
  fileUploadJSON: any = [];
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  displayStyle: any = 'none';
  fileUploadPopup: any = 'none';
  popupDisplayStyle: any = 'none';
  notificationOpen: boolean = false;
  notificationHeader: string = '';
  notificationBody: string = '';
  notificationType: any = '';
  notificationPosition: any = 'top-right';
  notificationDuration: number = 3000;
  showMessage: boolean = false;
  displayMessage: string = '';
  ruleLevel: string = '';
  showHistory: boolean = false;
  displayDuplicateMessage: boolean = false;
  mainDetailsResponse: any = {};
  generalDetailsResponse: any = {};
  additionalDetailsResponse: any = {};
  public editFormData: any = {};
  httpRequestdata: any = {
    url: './assets/json/form.json',
    dataRoot: 'src',
  };
  mainDetailsFormEvent: any;
  generalDetailsFormEvent: any;
  additionalDetailsFormEvent: any;
  retroApply: boolean = false;
  mainFormValuesOnLanding = [];
  mainFormValuesAfterEdit = [];
  bypassApply: boolean = false;
  headerLevel: boolean = false;
  relationSHJSON: any[] = [];
  generalDetailsJson: any[] = [];
  additionalDetailsJson: any[];
  ruleTypes: any = [];
  businessOwners: any = [];
  showForms: boolean = false;
  ruleSubTypes: any = [];
  calculationFields: any = [];
  lookBackPeriodValues: any = [];
  laggingPeriodValues: any = [];
  ltrWaitDurationOvp2: any = [];
  rule: any = {};
  isEdited: boolean = false;
  showLoader: boolean = false;
  fileDetailsExcelOpenModel: boolean = false;
  switchToggleNames: any = { 'onText': 'Value', 'offText': 'CFF' };
  operators: any = OperatorsRulesQB;
  userId: string = "";
  querySpecDetailsResponse: any = {};
  querySpecDetailsFormEvent: any = {};
  masterDataFromAPI: any;
  conceptSelected: string = "";
  queryBuilderClientDataset: any = [];
  openFileUploadConfirmModal: boolean = false;
  openConfirmationModal: boolean = false;
  qbFilled: boolean = true;
  ruleLevelFormEvent: any;
  showConfirmSubmitModal: boolean = false;
  openbypassConfirm: boolean = false;
  hidepopup: boolean = true;
  inventoryStatusOptions = {
    expiration: 'Expired',
    exception: 'Excluded',
    onhold: 'On Hold',
    noRecovery: 'No Recovery',
    lag: 'Lag Wait'
  };
  isFormChanges: boolean = false;
  showQueryBuilderComponents: boolean = true;
  customSql: string = "";
  dependentFieldsData: any = [
    {
      hide: ['rule_subtype', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'exception',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'onhold',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'noRecovery',
    },
    {
      hide: ['lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'expiration',
    },
    {
      hide: ['lookup_dates', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'lag',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'letters',
    }
  ];
  dependentLetterData: any = [
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'client_name', 'audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'overpayment',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    }
  ];

  dependentsubRuleData: any = [
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'client_name', "number_of_reminder_letter", 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['audit_type', 'release_by', 'provider', 'lagging_period', 'letter_concept_type', 'max_no_of_claims_per_letter', 'letter_wait_duration_ovp_2'],
      when: 'duration',

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidation'

    },
    {
      hide: ['audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'calculation_fields', 'lagging_period', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidations'

    }
  ];
  dependentsubRuleDurationData: any = [
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: 0,

    },
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: null,
    },
  ]
  onTabSelection(event) {
    event.name == 'Rule History' ? (this.showHistory = true, this.selectedTabIndex = 1) : (this.selectedTabIndex = 0);
  }
  /**
   * breadcrumSelection Funtion
   */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }
  public qbQuery = {
    condition: 'and',
    rules: [
      {
        field: '',
        operator: 'Equal',
        value: '',
        static: true,
        active: true,
      }
    ],
  };

  public qbLetterQuery = {
    condition: 'and',
    rules: [
      {
        field: 'CLNT_ID',
        operator: 'Equal',
        value: 65,
        static: true,
        active: true,
      }
    ],
  };
  //public qbQuery = {};
  qbConfig: any = {
    fields: {
      conceptID: {
        name: 'ConceptID',
        type: 'numeric',
        mutuallyExclusive: ['client'],
      },
      memberID: { name: 'MemeberID', type: 'text' },
      DOB: { name: 'DOB', type: 'calendar' },
      market: {
        name: 'Market',
        type: 'multipleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      country: {
        name: 'Country',
        type: 'singleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      age: { name: 'age', type: 'numeric', regPattern: '^\\d+$', regexMsg: 'Enter only positive numbers', forceRegex: true },
      client: {
        name: 'client',
        type: 'text',
        mutuallyExclusive: ['conceptID'],
      },
    },
    validations: {
      unique: ['CLNT_ID', 'CNCPT_ID']
    }
  };

  fileDetailsSectionJson: any[] = [
    {
      label: 'Comments',
      group: '',
      type: 'textarea',
      name: 'comments',
      column: '2',
      groupColumn: '1',
      disabled: false,
      value: '',
      placeholder: 'Enter comments here...',
      required: true
    },
  ];

  columnConfigforFileUploadtable: any = {
    switches: {
      enableSorting: true,
      enablePagination: true,
      enableFiltering: true,
    },
    colDefs: [
      {
        "name": "FILE NAME",
        "field": "file_name",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "COMMENTS",
        "field": "comments",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "ATTACHED BY",
        "field": "attached_by",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "SAVED DATE",
        "field": "saved date",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      }
    ]
  };

  multipleCriteriaRule: boolean = false;
  corpusId: string = "";
  customFields: any = [];
  sgDashboardDataset: any = [
    { id: 'standardQB', label: 'Standard', checked: true },
    { id: 'uploadQB', label: 'Upload File' }
  ];
  isStandardQBSelected: boolean = true;
  showQBuilder: boolean = true;
  showSubmit: boolean = true;
  multiCriteriaFile: any = {};
  querySpecificationJson: any = [];
  customSqlJson: any;
  showCustomSqlJson: boolean = false;
  unSelectedIndex: number = 0;
  showSegmentedControl: boolean = true;
  disableUploadBtn: boolean = true;
  uploadFileStatus: string = "";
  uploadFileStatusMsg: string = "";
  fileParserFileAccept: string = '.csv';
  openImpactReportPopup: boolean = false;
  fileParserTableProperties: any = {
    "enableSorting": false,
    "enablePagination": true,
    "editable": false,
    "enableFiltering": false,
    "isExcelExportNeeded": false,
    "isToggleColumnsNeeded": false,
    "isAddNeeded": false,
    "isSaveNeeded": false,
    "isDeleteNeeded": false,
    "isBulkUploadNeeded": false,
    "isRowSelectable": false
  }

  cellValueChanged(event: Event): void { }

  cellClicked(event: any): void { }

  /**
   * shows the upload file modal
  */
  uploadFileInEditRule() {
    this.fileUploadEditJSON = [];
    this.isDisabled = true;
    this.fileDetailsExcelOpenModel = true;
    this.isFileReady = true;
    this.isTextReady = true;
    this.fileUploadPopup = 'none';
    this.showMessage = false;
    this.displayDuplicateMessage = true;
    this.showMaxLimitMsg = false;
    this.fileUploadPopup = 'block';
  }

  fileDetailsExcelClosePopup = () => {
    this.fileDetailsExcelOpenModel = false;
  }

  /**
     * Method will be called to send file and Query builder mapping to be saved 
     */
  multipleCriteriaFileUpload(): void {
    this.recursiveFuncForCheckingEmptyField(this.qbQuery[constants.RULES]);

    if (!this.qbFilled) {
      this.uploadFileStatus = constants.ATTENTION;
      this.uploadFileStatusMsg = constants.FILL_QB_STATUS_MESSAGE;
      this.openFileUploadConfirmModal = true;
    }
    if (this.qbFilled) {
      const formData = new FormData();
      let conditions = [];
      conditions.push(this.modifyQBuilderStructure(this.qbQuery));
      Object.keys(this.multiCriteriaFile).forEach(key => {
        formData.append("file", this.multiCriteriaFile[key]);
      });
      formData.append("conditions", JSON.stringify(conditions));
      formData.append("rule_id", JSON.stringify(this.ruleId))
      this.corpusId != "" ? formData.append("corpus_id", JSON.stringify(this.corpusId)) : ""
      this.showLoader = true;
      this.RulesApiService.uploadFileAndQBCriteria(formData, this.levelIndicator)
        .subscribe(
          data => {
            if (data) {
              this.showSubmit = true;
              this.corpusId = data?.result?.uploaded_files[0]?.corpus_id;
              this.showLoader = false;
              this.uploadFileStatus = SUCCESS;
              this.uploadFileStatusMsg = FILE_UPLOAD_MSG;
              this.openFileUploadConfirmModal = true;
              this.removeFileParserTable();
            }
          },
          error => {
            this.showLoader = false;
            this.uploadFileStatus = FAIL;
            this.uploadFileStatusMsg = error.statusText;
            this.openFileUploadConfirmModal = true;
          });
    }
  }

  /**
   * Remove table from the file parser micro front end
   */
  removeFileParserTable(): void {
    let tableDivElement = document.querySelector('div.sheetsData-container');
    let queryBuilderDivElement = document.querySelector('div.pad-1rem');
    queryBuilderDivElement.classList.add('pad-1rem-cursor');
    tableDivElement.remove();
  }

  /*
* recursive function to check empty field in querybuilder
*/
  recursiveFuncForCheckingEmptyField(event) {
    this.qbFilled = true;
    for (let i = 0; i < event.length; i++) {
      if (event[i].value == "" || event[i].value == constants.SELECT) {
        this.qbFilled = false;
        return;
      } else if (event[i].rules) {
        this.recursiveFuncForCheckingEmptyField(event[i].rules);
      }
    }
  }

  /**
    * Clears QueryBuilder and closes the confirmation popup
   */
  clearQB() {
    this.showQBuilder = false;
    delete this.qbConfig.customFieldList;
    if (Object.keys(this.qbConfig.fields).length) {
      this.qbQuery = JSON.parse(JSON.stringify(qbQueryDefault));
      this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[2];
    }
    if (this.unSelectedIndex == 1) {
      this.isStandardQBSelected = true;
      this.showSubmit = true;
      this.corpusId = "";
      this.multiCriteriaFile = {};
      this.disableUploadBtn = true
    }
    else {
      this.isStandardQBSelected = false;
      this.showSubmit = false;
    }
    setTimeout(() => this.showQBuilder = true, 50);
    this.openConfirmationModal = false;
  }
  /**
   * Resets upload file modal
  */
  fileUploadpopUpReset() {
    this.isFileReady = false;
    this.isTextReady = false;
    this.fileUploadPopup = "none";
  }

  /**
   * closes the upload file modal
  */
  closePopupUploadForEditRule() {
    this.fileUploadpopUpReset();
  }

  onSubmitSkipClickedEitRule() {
    this.fileUploadpopUpReset();
  }

  /**
   * Triggers on change event of dynamic form
  */
  mapValuesToUploadJson(event: any) {
    this.postUploadDataJson = {
      "commentsInUpload": event.value['comments']
    }
    this.checkValidationForUploadFile();
  }

  /**
  * Validate the form to make submit button enable/disable
*/
  checkValidationForUploadFile() {
    this.isDisabled = true;
    if (this.fileUploadEditJSON && this.fileUploadEditJSON[0] !== undefined && this.postUploadDataJson && this.postUploadDataJson.commentsInUpload !== undefined && this.postUploadDataJson.commentsInUpload.trim() !== '' && !this.showMaxLimitMsg) {
      this.isDisabled = false;
    }
  }

  /**
   * Function to get Duplicate Data Json
   */
  getConfigForDuplicateRules() {
    this.RulesApiService.getColumnConfigJsonDuplicate(this.modalColumnConfigDuplicate).subscribe((data) => {
      this.columnConfigDuplicatePopup = data;
    })
  }

  /**
    * Validate the file size to make submit button enable/disable
    * Disable the Submit button if file size exceeds 25mb = 26214400bytes
  */
  validateMaxFileSize() {
    let combinedSize = 0;
    Object.keys(this.fileUploadEditJSON).forEach(key => {
      combinedSize += this.fileUploadEditJSON[key].size;
    });
    let isFileDisable = (combinedSize > 26214400) ? true : false;
    return isFileDisable;
  }

  /**
   * Triggers on click of submit on file uplaod modal
  */
  onSubmitUploadClickedEditRule() {
    this.isLoading = true;
    const formData = new FormData();
    Object.keys(this.fileUploadEditJSON).forEach(key => {
      formData.append("file", this.fileUploadEditJSON[key]);
    });
    formData.append("solution_id", 'claimsol1');
    formData.append("rule_id", this.ruleId.toString());
    formData.append("comments", this.postUploadDataJson.commentsInUpload);
    formData.append("attached_by", this.userId);
    formData.append("saved_date", this.dateService.formatDate());
    this.RulesApiService.addFilesToRules(formData, this.levelIndicator.split(" ")[0])
      .subscribe(
        data => {
          if (data) {
            this.isLoading = false;
            this.alertService.setSuccessNotification({
              notificationHeader: "Success",
              notificationBody: 'File successfully attached to the rule',
            });
            this.populateAdditionalDetails(data.result.uploaded_files[0]);
          }
          this.callGetFileDetailsRules(this.levelIndicator.split(" ")[0], this.versionSeq);
        },
        error => {
          this.isLoading = false;
          this.alertService.setErrorNotification({
            notificationHeader: "Fail",
            notificationBody: `${error.statusText}`,
          });
        });
    this.isFileReady = false;
    this.isTextReady = false;
    this.fileUploadPopup = "none";
  }

  moveToOptionSelected(event: Event): void { }

  tableReady(event) { }

  /**
   * Gets triggered on click event on upload button
  */
  upload(event: Event): void {
    this.fileUploadEditJSON = event;
    let fileSizeMaxedOut = this.validateMaxFileSize();
    if (fileSizeMaxedOut) {
      this.showMaxLimitMsg = true;
    }
    else {
      this.showMaxLimitMsg = false;
    }
    this.checkValidationForUploadFile();
  }

  /**
   * To remove highlighting from fieds which passed validation
  */
  resetValidFields() {
    const collection = document.querySelectorAll(
      `marketplace-select.ng-valid .select-holder .ng-select .ng-select-container,
      marketplace-input.ng-valid input,
      marketplace-textarea.ng-valid .textarea-holder textarea,
      marketplace-date-picker.ng-valid marketplace-input input`
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].classList.remove('redBorder');
    }
  }

  /**
   * To highlight fieds which failed validation
  */
  showAllInvalidFields() {
    this.editErrOpenModel = true;
    this.resetValidFields();
    const invalidCollection = document.querySelectorAll(
      `marketplace-select.ng-invalid .select-holder .ng-select .ng-select-container,
      marketplace-input.ng-invalid input,
      marketplace-textarea.ng-invalid .textarea-holder textarea,
      marketplace-date-picker.ng-invalid marketplace-input input`
    );
    for (let i = 0; i < invalidCollection.length; i++) {
      invalidCollection[i].classList.add('redBorder');
    }
    if (this.isNull(this.selectedValue)) {
      let statusField = document.getElementsByName("inventory-status");
      statusField.forEach(field => field.classList.add('redBorder'));
    }
    this.popupDisplayStyle = 'block';
  }

  editErrClosePopup = () => {
    this.editErrOpenModel = false;
  }

  /**
   * checks for null value
  */
  isNull(fieldValue) {
    if (fieldValue == null || fieldValue == "") return true;
    else return false;
  }

  /**
   * Changing the query builder structure to be sync with API
  */
  modifyQBuilderStructure(qbQuery) {
    const operatorMap = OperatorsMapForQb;
    let startVal;
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case "condition":
          this.log = v;
          break;
        case "rules":
          this.conditions = v;
          break;
        case "field":
          this.json_path = "$";
          this.lval = v;
          break;
        case "startValue":
          startVal = JSON.parse(JSON.stringify(v));
          this.rval = { "start": startVal, "end": '' }
          break;
        case "endValue":
          this.rval = { "start": startVal, "end": v }
          break;
        case "value":
          if (v != null && v != 'undefined' &&
            v.toString().indexOf(',') > -1) {
            this.rval = v.toString().split(',');
          }
          else {
            this.rval = v;
          }
          break;
        case "operator":
          this.op = operatorMap[v];
          break;
        case "config":
        case "operatorList":
        case "delete":
        case "fieldsList":
        case "fieldsMapList":
        case "customfieldsList":
        case "tabsList":
          delete qbQuery[k];
          break;
        default:
          return v;

      }
    });
    return parsed;
  }

  editErrOpenModel: boolean = false

  /**
   * @returns the difference of two arrays to show in Concepts change Pop-Up
   */
  symmetricDiffOfConcetps(alreadyPresentConcepts, conceptsAfterChanges) {
    const diff1 = alreadyPresentConcepts.filter(e => !conceptsAfterChanges.includes(e));
    const diff2 = conceptsAfterChanges.filter(e => !alreadyPresentConcepts.includes(e));

    return [...diff1, ...diff2];
  }

  /**
    * To close the concepts diff pop-up
   */
  conceptsChangedPopUpClose() {
    this.conceptsChangedPopUp = false;
  }

  /**
    * To show the concepts diff pop-up
   */
  showConceptsChanges() {
    this.addedConcepts = [];
    this.deletedConcepts = [];
    //put a condition to check if concepts are changed
    //if that condition is true then you have to show a popup otherwise let the flow go on
    let diffOfCurntAndPrvs = this.symmetricDiffOfConcetps(this.orgnlCnptCarriedfrmLstEdit, this.compatibleJsonForConcepts);
    if (this.conceptsChanged && diffOfCurntAndPrvs.length != 0) {
      diffOfCurntAndPrvs.forEach(e => {
        if (this.compatibleJsonForConcepts.includes(e)) {
          this.addedConcepts.push(e);
        } else {
          this.deletedConcepts.push(e);
        }
      });
      this.conceptsChangedPopUp = true;
    } else {
      this.SubmitConfirm();
    }
  }

  /**
   * Validating all the dynamic forms for the mandatory fields 
   * and highlighting the failed fields by calling showAllInvalidFields()
  */
  validateEditDynamicForms(buttonType: string) {
    this.mainFieldsNullCheckCount = 0;
    //25.1 - this.relationSHJSON[0]["groupControls"].forEach(ele => {
    //   if (ele.visible) {
    //     if (this.mainDetailsFormEvent['controls']['rules']['controls'][ele.id]['status'] == constants.INVALID) {
    //       this.mainFieldsNullCheckCount++
    //     }
    //   }
    // });
    if (buttonType == 'submit' && this.bypassApply && this.isDraftRule) {
      this.openbypassConfirm = true;
    }
    else {
      this.hidepopup = false;
      if (buttonType == 'submitbypass') {
        buttonType = 'submit';
        this.closebypassConfirm();
      }
      this.hidepopup = true;
      //25.1 - if ((this.isDefined(this.generalDetailsFormEvent) && this.generalDetailsFormEvent['status'] == 'INVALID') ||
      //   this.isNull(this.selectedValue) || this.setStatusOfRuleLevel || (this.mainFieldsNullCheckCount > 0)) 
      if ((this.isDefined(this.generalDetailsFormEvent) && this.generalDetailsFormEvent['status'] == 'INVALID') ||
        this.isNull(this.selectedValue) || this.setStatusOfRuleLevel) {
        this.showAllInvalidFields();
        return;
      } else {
        this.resetValidFields();
      }
      if (this.isDefined(this.generalDetailsFormEvent)) {
        Object.keys(this.generalDetailsResponse).forEach((group) => {
          Object.keys(this.generalDetailsResponse[group]['value']).forEach(
            (formControl) => {
              this.rule[formControl] = this.generalDetailsResponse[group]['value'][formControl];
            }
          );
        });
      }
      if (this.isDefined(this.additionalDetailsFormEvent)) {
        this.rule['external_point_of_contact'] = this.additionalDetailsResponse['additionalDetailsTop']['value']['external_point_of_contact'];
      }
      if (this.isDefined(this.mainDetailsResponse)) {
        Object.keys(this.mainDetailsResponse).forEach((group) => {
          Object.keys(this.mainDetailsResponse[group]['value']).forEach(
            (formControl) => {
              this.rule[formControl] = this.mainDetailsResponse[group]['value'][formControl];
            }
          );
        });
      }
      this.isDraftSubmission = (buttonType == 'save') ? true : false;
      if (buttonType == 'submit' && this.isDraftRule)
        this.showConfirmSubmitModal = true;
      else
        this.showConceptsChanges();
    }
  }
  /**
    * To goahead with Submission when Ok is Clicked on Submission Confirmation Modal
   */
  SubmitConfirm() {
    this.showConfirmSubmitModal = false;
    this.conceptsChangedPopUp = false;
    this.editRule();
  }

  /**
   * To Cancel Submission when Cancel is Clicked on Submission Confirmation Modal
  */
  cancelSubmission() {
    this.showConfirmSubmitModal = false;
  }

  /**
   * To close Upload Modal
  */
  closeFileUploadModal(): void {
    this.openFileUploadConfirmModal = false;
  }

  /**
  * Validate query builder fields and evaluate rule level
  */
  validateEdit() {
    if (this.levelIndicator == constants.GLOBAL_LEVEL) {
      this.showMessage = true;
      this.displayDuplicateMessage = false;
      this.displayMessage =
        'You are about to create a Global Rule that will affect all clients, concepts and insights.';
      this.displayStyle = 'block';
    } else {
      this.editRule();
    }
  }

  /**
   * Making flag true to show duplicate modal
  */
  checkForDuplicateRules() {
    this.editSubmitOpenModel = true;
    this.showMessage = false;
    this.displayDuplicateMessage = true;
    this.displayStyle = 'block';
    setTimeout(() => (this.tableRedraw = Date.now()), 100);
  }

  /**
   * Which closes the current screen and show dashboard
  */
  cancelEdit() {
    this.router.navigate([`${this.breadcrumbDataset[1].url}`]);
  }

  /**
   * closes all the modal
  */
  closePopup() {
    this.editSubmitOpenModel = false;
    this.displayStyle = 'none';
    this.popupDisplayStyle = 'none';
    this.showLoader = false;
  }

  /**
  * Closes confirmation modal
 */
  closeConfirmationModal() {
    this.showSegmentedControl = false;
    this.openConfirmationModal = false;
    this.sgDashboardDataset.forEach(element => {
      element.checked = false;
    });
    this.sgDashboardDataset[this.unSelectedIndex].checked = true;
    if (this.unSelectedIndex == 0) {
      this.isStandardQBSelected = true;
      this.showSubmit = true;
    }
    else {
      this.isStandardQBSelected = false;
      this.showSubmit = false;
    }
    setTimeout(() => this.showSegmentedControl = true, 50);
  }

  /**
   * set the retro aplly property to a local variable
  */
  setRetro(event) {
    this.rule['retro_apply'] = event.toggle;
    this.retroApply = event.toggle;
    this.isEdited = true;
  }

  /**
   * set the bypass property to a local variable
  */
  setBypass(event) {
    this.rule['bypass_apply'] = event.toggle;
    this.bypassApply = event.toggle;
    this.isEdited = true;
  }

  /**
 * set the headerlevel property to a local variable
*/
  setLevel(event) {
    this.rule['header_level'] = event.toggle;
    this.headerLevel = event.toggle;
    this.isEdited = true;
  }

  editSubmitOpenModel: boolean = false;

  /**
   * Calls the service method to call Edit API
  */
  editRule() {
    this.showLoader = true;
    this.rule.request_type = "save";
    this.rule.inventory_status = this.selectedValue;
    this.rule[constants.START_DATE] = this.dateService.getECPDateFormat(this.rule.start_date);
    this.rule[constants.END_DATE] = this.dateService.getECPDateFormat(this.rule.end_date);

    this.rule.rule_level = this.levelIndicator?.split(" ")[0];
    if (this.rule.calculation_fields) this.rule.calculation_fields = [this.rule.calculation_fields];
    if (this.rule.lookup_dates) this.rule.lookup_dates = { "value": this.rule.lookup_dates, "type": "month" };
    if (this.rule.lagging_period) this.rule.lagging_period = { "value": this.rule.lagging_period, "type": "day" };
    this.rule.retro_apply = this.retroApply;
    this.rule.bypass_apply = this.bypassApply;
    this.rule.header_level = this.headerLevel;

    if (this.clientIdSelected && this.rule.client_name) {
      this.clientIdSelected = this.selectedProfileClientId;
    }
    if (this.levelIndicator == constants.CLIENT_LEVEL && this.clientIdSelected) {
      this.rule.clientId = this.clientIdSelected;
    }
    if ((this.levelIndicator == constants.CONCEPT_LEVEL || this.rule.rule_type == constants.LAG) && this.conceptIdSelected.length > 0)
      this.rule.concept = this.conceptIdSelected;

    if (this.levelIndicator == constants.GLOBAL_LEVEL && !this.rule.client_name) {
      this.rule.clientId = null;
      this.rule.client = "";
      this.rule.concept = [];
      this.rule.client_name = "";
    }
    else if ((this.levelIndicator == constants.GLOBAL_LEVEL && this.rule.client_name) || this.levelIndicator == constants.CLIENT_LEVEL) {
      this.rule.concept = [];
    }
    else if (this.levelIndicator == constants.CONCEPT_LEVEL && !this.rule.client_name) {
      this.rule.client = this.selectedProfileClientName;
      this.rule.clientId = this.selectedProfileClientId;
      this.rule.client_name = null;
    }

    this.rule.grace_period_in_days = this.rule.grace_period_in_days != "" ? this.rule.grace_period_in_days : null;
    this.rule.max_no_of_claims_per_letter = this.rule.max_no_of_claims_per_letter != "" ? this.rule.max_no_of_claims_per_letter : null;

    this.rule[constants.UPDATED_BY] = this.userId;
    this.rule[constants.UPDATED_DATE] = this.dateService.formatDate();
    this.conceptIdSelected == "" ? this.rule.concept = [] : this.rule.concept = this.conceptIdSelected;
    //commenting because of not seeing any point. Will be removed once testing is done
    //this.rule.client = this.clientIdSelected || this.rule.client;
    //this.rule.clientId = this.clientIdForECP || this.rule.clientId;
    if (this.rule.client_name) this.rule.client_name = this.rule.client_name.toString();
    this.rule.conditions = [];
    this.rule.is_draft = this.isDraftSubmission;
    this.rule.edited_by = this.userId;


    //need to remove below code after 25.1 deployment
    this.rule.is_submitted = true

    //25.1- if (this.isDraftSubmission && !this.isDraftRule) {
    //   this.rule.status = "edit"
    //   this.rule.is_edited = true
    //   this.rule.is_submitted = false
    // }
    // else {
    //   this.rule.is_edited = false
    //   this.rule.is_submitted = true
    // }
    this.rule.created_by = this.createdBy
    if (this.rule.rule_type == "letters") {
      //removed once testing is done
      //var tstval = this.qbQuery.rules.filter(f => f.value == "");
      //var tstselectval = this.qbQuery.rules.filter(f => f.value == "select");
      this.qbLetterQuery.rules[0].value = this.selectedProfileClientId;
      if (this.qbQuery.rules.length == 0)
        this.rule.conditions.push(this.modifyQBuilderStructure(this.qbLetterQuery));
      else
        this.rule.conditions.push(this.modifyQBuilderStructure(this.qbQuery));
    }
    else {
      if (this.showQueryBuilderComponents) {
        this.rule.conditions.push(this.modifyQBuilderStructure(this.qbQuery));
        this.rule.execution_type = "";
      }
      else {
        this.rule.conditions.push({ "query": this.customSql });
        this.rule.execution_type = "sql_query";
      }
    }
    if (this.corpusId != "") {
      this.rule.rule_metadata = { "corpus_id": this.corpusId };
    }

    //might need to remove below code after 25.1 deployment
    delete this.rule["undefined"]

    let editRuleRequest = {
      data: this.rule,
      "created_ts": this.dateService.getDbgDateFormat(Date.now())
    };

    this.RulesApiService.createEditRule(editRuleRequest).subscribe(
      (data) => {
        let duplicateSwitchCase: any;
        duplicateSwitchCase = data.duplicates_present == true ? true : data?.status?.code == 200 ? 200 : 500;
        this.showLoader = false;
        switch (duplicateSwitchCase) {
          case true:
            this.duplicateRuleTableJson = [];
            let getDuplicateResults = [];
            let mergingResults: any = data.duplicate_rules;
            this.duplicateRuleTableJson = getDuplicateResults.concat(mergingResults);
            this.duplicateRuleTableJson.map((s, i) => {
              s.id = i + 1;
              if (s.rule_type == 'letters')
                s.rule_subtype = s.ltr_rule_sub_type;
            });
            this.checkForDuplicateRules();
            break;
          case 200:
            this.openImpactReportPopup = true;
            this.isEdited = false;
            this.showHistory = false
            break;
          case 500:
            this.alertService.setErrorNotification({
              notificationBody: data.status?.message ? data.status?.message : INSIGHT_CLAIM_CONSTANTS.VERIFICATION_FAILURE_MSG,
            });
            break;
          default:
            break;
        }
      },
      (error) => {
        this.alertService.setErrorNotification({
          notificationBody: error,
        });
        this.showLoader = false;
      }
    );
  }

  /**
   * Close save successful popup
   */
  savedConfirmPopupClose() {
    this.openImpactReportPopup = false;
  }

  /**
   * Navigates to impact report screen of rule's effect on past executions.
   */
  generatePreview() {
    this.router.navigate([`/rules/impact-report/${this.ruleIdForGeneratePreview}`], { queryParams: { level: this.rule.rule_level } });
    this.openImpactReportPopup = false
  }

  /**
    * To be implemented once delete api ready from ECP team
   */
  discardSavedRule() {
    this.showLoader = true;

    let deletePayload: any = {
      rule_id: this.rule.rule_id,
      is_draft: this.rule.is_draft,
    };
    this.isEditedRule ? deletePayload.is_edited = true : ""
    let headers = {
      source: "DBG",
    };
    if (this.rule.rule_level === constants.GLOBAL) {
      deletePayload.rule_level = "Global"
    }
    let deleteRequest = {
      data: deletePayload
    };

    this.RulesApiService.deleteRule(deleteRequest, headers).subscribe(data => {
      if (data.status.code == 200 && (data.result.metadata?.deleted || data.result.metadata?.msg.includes("successfully"))) {
        this.alertService.setSuccessNotification({
          notificationHeader: `Rule Successfully discarded`,
          notificationBody: `Rule Id : ${data.result.metadata.rule_id}`
        });
        this.showLoader = false;
        this.cancelEdit();
      }
      else if (data.status.code == 500) {
        this.alertService.setErrorNotification({
          notificationBody: data.status.message,
        });
        this.showLoader = false;
      }
      else {
        {
          this.alertService.setErrorNotification({
            notificationBody: data.error,
          });
          this.showLoader = false;
        }
      }
    }),
      (error) => {
        this.alertService.setErrorNotification({
          notificationBody: error,
        });
        this.showLoader = false;
      }
  }

  editSubmitClosePopup = () => {
    this.editSubmitOpenModel = false;
  }

  recentQueryList = [
    {
      Name: 'Criteria One',
      'Rule Type': 'Global',
      'Rule SubType': 'Global',
      'Created By': 'Lakki Reddy',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'and',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '22',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: '100',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Two',
      'Rule Type': 'Global',
      'Rule SubType': 'Global',
      'Created By': 'Ajan Srinivas',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'and',
        rules: [
          {
            field: 'conceptID',
            operator: 'Equal',
            value: '55',
            static: true,
            active: true,
          },
          {
            field: 'memberID',
            operator: 'Equal',
            value: '400',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Three',
      'Rule Type': 'Regional',
      'Rule SubType': 'Local',
      'Created By': 'User 3',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'or',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '40',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: '12',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Four',
      'Rule Type': 'Regional',
      'Rule SubType': 'Local',
      'Created By': 'User 4',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'or',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '40',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: 'Smith',
            static: true,
            active: true,
          },
        ],
      },
    },
  ];

  /**
   * Method triggers on file selection
   * @param event 
   */
  uploadMultiCriteriaFile(event: Event): void {
    this.showQBuilder = false;
    if ((Array.isArray(event) && event.length == 0) || event['changed'] == "") {
      this.disableUploadBtn = true;
    }
    else {
      this.disableUploadBtn = false;
    }
    delete this.qbConfig.customFieldList;
    this.showSubmit = false;
    this.multiCriteriaFile = event;
    setTimeout(() => {
      this.showQBuilder = true;
      this.enableQueryBuilder();
    }, 50);
  }

  /**
 * Method invoked when File Parser completes loading the table
 * @param event 
 */
  onParseComplete(event) {
    this.showQBuilder = false;
    this.qbConfig.customFieldList = {};
    this.qbConfig.customFieldList.dataset = [];
    let parserDataset = event?.sheet;
    parserDataset.forEach(element => {
      Object.keys(element.dataJSON[0]).forEach(column => {
        if (column != "")
          this.qbConfig.customFieldList.dataset.push({ "name": column, "id": column, "collection": "Worksheet Headers" });
      });
    });
    this.showQBuilder = true;
    this.isEdited = true;
  }

  /**
   * All below methods are for Query builder evaluation to get rule level
  */
  dropRecentList(event) {
  }

  qbFieldChange(event) {
    this.isEdited = true;
  }
  qbChange(event) {

  }

  /**
   * triggers on change of mainForm dynamic form
  */
  mapValuesFromMainToJson(event) {

    this.mainFormValuesAfterEdit = []
    this.mainDetailsResponse = event.controls;
    this.mainDetailsFormEvent = event;
    this.rule['release_by'] = this.mainDetailsResponse.rules.value.release_by;
    if (this.isFormChanges == false) {
      this.relationSHJSON[0]["groupControls"].forEach(ele => {
        if (ele.label == constants.GRACE_PERIOD && !ele.visible) {
          this.mainDetailsFormEvent['controls']['rules']['controls']["grace_period_in_days"]['value'] = ""
          ele.value = ""
        }
      });
      (this.isDraftRule || this.isEditedRule || this.isDataReceivedFromSubcription) ? this.isEdited = true : this.isEdited = false;
      Object.keys(this.mainDetailsResponse.rules.value).forEach((group) => {
        let ele = this.mainDetailsResponse.rules.value[group]
        this.mainFormValuesOnLanding.push(ele)
      })
      this.isFormChanges = true
    }
    Object.keys(this.mainDetailsResponse.rules.value).forEach((group) => {
      let element = this.mainDetailsResponse.rules.value[group]
      this.mainFormValuesAfterEdit.push(element)
    })

    //25.1 - for (let ele = 0; ele < this.mainFormValuesOnLanding.length; ele++) {
    //   this.mainFormValuesOnLanding[ele] == "" || this.mainFormValuesOnLanding[ele] == null ? this.mainFormValuesOnLanding[ele] = null : ""
    //   this.mainFormValuesAfterEdit[ele] == "" || this.mainFormValuesAfterEdit[ele] == null ? this.mainFormValuesAfterEdit[ele] = null : ""

    //   if (this.mainFormValuesOnLanding[ele] != this.mainFormValuesAfterEdit[ele]) {
    //     this.isEdited = true;
    //   }
    // }
    let ruleTypeForValidation = this.mainDetailsFormEvent.value.rules.rule_type;
    if (this.isDefined(ruleTypeForValidation)) {
      switch (ruleTypeForValidation) {
        case 'expiration':
          this.selectedValue = this.inventoryStatusOptions.expiration;
          break;
        case 'exception':
          this.selectedValue = this.inventoryStatusOptions.exception;
          break;
        case 'onhold':
          this.selectedValue = this.inventoryStatusOptions.onhold;
          break;
        case 'noRecovery':
          this.selectedValue = this.inventoryStatusOptions.noRecovery;
          break;
        case 'lag':
          this.selectedValue = this.inventoryStatusOptions.lag;
          break;
        case 'letters':
          {
            this.selectedValue = "Awaiting Adjustment";
            this.suggestionWindow = false;
            if (this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != null && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidation' && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidations') {
              this.enableInventoryStatus = true;
            }
            else
              this.enableInventoryStatus = false;
            if (this.mainDetailsFormEvent.value.rules.letter_type == "overpayment")
              this.selectedValue = "Awaiting Adjustment";
            else if (this.mainDetailsFormEvent.value.rules.letter_type == "disregard")
              this.selectedValue = "Closed";

            break;
          }
        default:
          break;
      }
      // this.enableInventoryStatus = true;
    }
    else {
      this.enableInventoryStatus = false;
    }
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * triggers on change of generalForm dynamic form
  */
  mapValuesFromGeneralToJson(event) {
    this.generalDetailsResponse = event.controls;
    this.generalDetailsFormEvent = event;
    this.isEdited = true;
    if (this.isFormChanges == false) {
      this.isEdited = (this.isDraftRule || this.isEditedRule || this.isDataReceivedFromSubcription) ? true : false;
      this.isFormChanges = true;
    }
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * triggers on change of additionalForm dynamic form
  */
  mapValuesFromAdditionalToJson(event) {
    this.additionalDetailsResponse = event.controls;
    this.additionalDetailsFormEvent = event;
    this.isEdited = true;
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * Triggers on child(ruleHistory) events
  */
  handleChildClick(event) {
    if (event.button == 'Cancel') {
      event.name = 'Edit Rule';
      this.onTabSelection(event);
      this.isDataReceivedFromSubcription = false;
      this.showQBuilder = false;
      this.showForms = false;
      this.callGetRuleApis();
    }
    else if (event.button == 'Reinstate') {
      this.reInstateRuleVersionDetails = JSON.parse((JSON.stringify(event)));
      delete this.reInstateRuleVersionDetails.headerString;
      delete this.reInstateRuleVersionDetails.qbQuery;
      delete this.reInstateRuleVersionDetails.subText;
      delete this.reInstateRuleVersionDetails.button;
      this.selectedTabIndex = 0;
      this.showQBuilder = false;
      this.showForms = false;
      this.isDataReceivedFromSubcription = true;
      this.callGetRuleApis();
    }
  }

  ngOnInit(): void {
    this.ruleId = Number(this.router.url.slice(this.router.url.lastIndexOf('/') + 1));
    this.headerText = `Edit Rule ${this.ruleId}`;
    this.selectedProfileClientId = Number(sessionStorage.getItem('clientId'));
    this.selectedProfileClientName = sessionStorage.getItem('clientName');
    this.callGetRuleApis();
    this.getConfigForDuplicateRules();
  }

  /**
   * Calls the service method to get files for the rule from API
  */
  callGetFileDetailsRules(ruleLevel, versionSeq) {
    this.RulesApiService.getFileDetailsOfRules(this.ruleId, ruleLevel, versionSeq).subscribe(data => {
      if (data) {
        if (data.status.code == 200 && this.isDefined(data.result) && this.isDefined(data.result.files)) {
          data.result.files.map((s, i) => {
            s.id = i + 1;
          });
          this.dataJSON = data.result.files;
        }
      }
    })
  }

  /**
   * Calls the service method to get rule info and master data from API
  */
  callGetRuleApis() {
    this.showLoader = true;
    let ruleInfo;
    let index = 0;
    this.RulesApiService.getAllViewEditRuleAPIs(this.ruleId).subscribe(
      (data) => {
        if (data) {
          if (data[0].status.code == 200 && data[1].status.code == 200) {
            let masterData = data[0].result.fields;
            if (!this.isDataReceivedFromSubcription) {
              for (let i = 0; i < 3; i++) {
                if (data[1].result.metadata?.rules[i]?.is_edited && DashboardComponent.isEditedDashBoard) {
                  index = i;
                  this.isEditedRule = true;
                }
              }
              ruleInfo = data[1].result.metadata.rules[index]
            }
            else if (this.isDataReceivedFromSubcription) {
              ruleInfo = this.reInstateRuleVersionDetails
            }
            this.ruleLevel = ruleInfo.rule_level;
            this.levelIndicator = ruleInfo.rule_level
            this.createdBy = ruleInfo.created_by
            this.ruleIdForGeneratePreview = ruleInfo.rule_id
            this.ltrWaitDurationOVP = ruleInfo.letter_wait_duration_in_days
            this.isDraftRule = data[1].result.metadata.rules[0].is_draft;
            this.isInputDisabled = this.isDraftRule ? false : true;
            this.isEdited = (this.isDraftRule || this.isEditedRule || this.isDataReceivedFromSubcription) ? true : false;
            this.refineMasterData(masterData, ruleInfo);
            this.versionSeq = ruleInfo.version_seq;
            this.callGetFileDetailsRules(ruleInfo.rule_level, this.versionSeq);
          } else {
            /* 
          notification part will be covered in the next sprint for success/error messages
          */
            console.log('Unsuccessful', data.status.traceback);
            this.showLoader = false;
          }
        }
      },
      (error) => {
        /* 
              notification part will be covered in the next sprint for success/error messages
          */
        this.showLoader = false;
      }
    );
  }

  /**
   * Refine master data into different objects for different fields
  */
  refineMasterData(masterDataFromAPI, ruleInfo) {
    this.masterDataFromAPI = masterDataFromAPI;
    let ruleTypeMasterData = masterDataFromAPI['rule_type'];
    let ruleFieldsIdMapping = { "rule_sub_type": "rule_subtype", "calculation_fields": "calculation_fields", "lookback_period": "lookup_dates", "lagging_period": "lagging_period", "letter_type": "letter_type", "provider": "provider", "type_of_days": "type_of_days", "letter_wait_duration_in_days": "letter_wait_duration_in_days", "grace_period_in_days": "grace_period_in_days", "concept": "letter_concept_type", "ltr_rule_sub_type": "ltr_rule_sub_type", "number_of_reminder_letter": "number_of_reminder_letter", "letter_wait_duration_ovp_2": "letter_wait_duration_ovp_2" };
    ruleTypeMasterData.forEach(ruleTypeObj => {
      /* All the keys inside ruleTypeObj is rule type */
      Object.keys(ruleTypeObj).forEach((ruleType) => {
        this.ruleTypes.push({ name: ruleType, value: ruleTypeObj[ruleType].value });
        Object.keys(ruleTypeObj[ruleType]).forEach((field) => {
          if (field != 'value') {
            /** Logic to push lookback period value if it is not there in the list */
            if (ruleInfo?.lookup_dates?.value != null && ruleFieldsIdMapping[field] === "lookup_dates") {
              let isElementPresent = false;
              ruleTypeObj[ruleType][field].forEach(element => {
                if (ruleInfo.lookup_dates.value === element.value) {
                  isElementPresent = true;
                }
              });
              if (!isElementPresent) {
                ruleTypeObj[ruleType][field].push({ "name": ruleInfo.lookup_dates.value, "value": Number(ruleInfo.lookup_dates.value) });
              }
            }

            if (ruleInfo?.lagging_period != null && ruleFieldsIdMapping[field] === "lagging_period") {
              let isElementPresent = false;
              ruleTypeObj[ruleType][field].forEach(element => {
                if (ruleInfo.lagging_period.value === element.value) {
                  isElementPresent = true;
                }
              });
              if (!isElementPresent) {
                ruleTypeObj[ruleType][field].push({ "name": ruleInfo.lagging_period.value, "value": Number(ruleInfo.lagging_period.value) });
              }
            }

            this.dependentFieldsData.push({
              updateDataset: [
                {
                  id: ruleFieldsIdMapping[field],
                  dataset: ruleTypeObj[ruleType][field],
                },
              ],
              when: ruleTypeObj[ruleType].value,
            });
            if (ruleFieldsIdMapping[field] === "letter_type") {
              let letterdata = ruleTypeObj[ruleType][field];
              letterdata.forEach(letterTypeObj => {
                Object.keys(letterTypeObj).forEach((subFields) => {

                  this.dependentLetterData.push({
                    updateDataset: [
                      {
                        id: ruleFieldsIdMapping[subFields],
                        dataset: letterTypeObj[subFields],
                      },
                    ],
                    when: letterTypeObj['value'],
                  });

                  let subRuleData = letterTypeObj['ltr_rule_sub_type'];
                  subRuleData.forEach(ruleSubObj => {

                    Object.keys(ruleSubObj).forEach((otherFields) => {


                      if (ruleFieldsIdMapping[otherFields] === "letter_wait_duration_in_days" || ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter" || ruleFieldsIdMapping[otherFields] === "lagging_period") {
                        ruleSubObj[otherFields].forEach(element => {
                          element.id = ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter" ? element.value.toString() : element.value;
                        });
                      }
                      //Field reuired only for medica and KC client
                      if (![constants.BCBSKC_CLIENT_ID, constants.MEDICA_CLIENT_ID].includes(Number(sessionStorage.getItem('clientId')))) {
                        this.dependentsubRuleData.forEach(subRuleData => {

                          if (subRuleData['when'] === 'duration' && subRuleData.hide) {
                            subRuleData.hide.push('number_of_reminder_letter');
                          }
                        });
                      }
                      if (otherFields == 'number_of_reminder_letter') {
                        Object.keys(ruleSubObj[otherFields][1]).forEach(letterDurationField => {
                          if (ruleFieldsIdMapping[letterDurationField] === "letter_wait_duration_ovp_2") {
                            ruleSubObj[otherFields][1][letterDurationField].forEach(element => {
                              element.id = element.value;
                            });
                          }
                          if (ruleSubObj[otherFields][1][letterDurationField].length > 1) {
                            this.dependentsubRuleDurationData.push({
                              updateDataset: [
                                {
                                  id: ruleFieldsIdMapping[letterDurationField],
                                  dataset: ruleSubObj[otherFields][1][letterDurationField],
                                },
                              ],
                              when: ruleSubObj[otherFields][1]['value'],
                            })
                          }
                        });

                      }

                      this.dependentsubRuleData.push({
                        updateDataset: [
                          {
                            id: ruleFieldsIdMapping[otherFields],
                            dataset: ruleSubObj[otherFields],
                          },
                        ],
                        when: ruleSubObj['value'],
                      });
                    });
                  });

                });
              });
            }
          }
        });
      });
    });
    this.businessOwners = masterDataFromAPI['business_owner'];
    this.qbConfig.fields = this.modifyQBConfig(masterDataFromAPI['query_fields']);
    delete this.qbConfig.fields.CLNT_ID;
    delete this.qbConfig.fields.CNCPT_ID;
    this.populateRuleDataOnForm(ruleInfo);
  }

  /**
   * Modify master data fields into the format query builder understands
  */
  modifyQBConfig(masterDataQBConfig) {
    let QBfields = {};
    let mutuallyExclusiveFields = { 'CLNT_ID': 'CNCPT_ID', 'CNCPT_ID': 'CLNT_ID', 'CNCPT_NM': 'CLNT_ID' };
    const typeMapping = {
      'decimal': 'numeric',
      'string': 'text',
      'date': 'calendar'
    };
    masterDataQBConfig.forEach(field => {
      switch (field.field_type) {
        case 'dropdown':
          QBfields[field.value] = { name: field.name, type: 'singleselect', dataset: field.options, key: 'name', id: 'id' };
          break;
        case 'freetext':
          QBfields[field.value] = { name: field.name, type: typeMapping[field.type] };
          if (field.type == 'date') {
            QBfields[field.value].dateFormat = 'YYYY-MM-DD';
          }
      }
      if (field.value == 'CLNT_ID' || field.value == 'CNCPT_ID' || field.value == 'CNCPT_NM') {
        QBfields[field.value].mutuallyExclusive = [mutuallyExclusiveFields[field.value]];
      }
    });
    return QBfields;
  }

  /**
   * Method TO show Description and similar codes according to Status Selected
   */
  onSelect(item) {
    this.isEdited = true;
    this.statusDescription = item?.cdValLongDesc ? item?.cdValLongDesc : "No Description Available for Selected Status";
    this.statusSuggestion = item.cdValShrtDesc;
    this.selectedValue = item.cdValName;
    this.searchResultsWindow = false;
    this.suggestionWindow = false;
    this.openAccordion = true;
  }
  /**
   * Method To get static data set for setting inventory status
   */
  getInventoryStatusData() {
    this.RulesApiService.getInventoryStatusData().subscribe((data) => {
      this.inventoryStatusDataset = data;

      setTimeout(() => (this.showDescriptionandInventoryStatus()), 100);
    });
  }

  /**
   * Method TO close search results if input box loses focus
   */
  inventoryInputfocusOut(ev: any) {
    if (this.filteredResults.length == 0) {
      setTimeout(() => (this.selectedValue = "", 100));
    }
    this.noResultsFound = false;
  }

  /**
  * Method To populate accordion with description according to status code selected
  */
  giveDescriptionForStatus(event: any) {
    var checkingField = event.target?.value ? event.target.value : "";
    if (checkingField != "") {
      var target = event.target.value.toLowerCase();
      this.filteredResults = this.inventoryStatusDataset.filter(character => {
        return character.cdValName.toLowerCase().includes(target);
      });
      this.searchResultsWindow = true;
      this.openAccordion = false;
      if (this.filteredResults.length == 1) {
        this.suggestionWindow = true;
        this.statusDescription = this.filteredResults[0].cdValLongDesc;
        this.statusSuggestion = this.filteredResults[0].cdValShrtDesc;
        this.selectedValue = this.filteredResults[0].cdValName;
        this.noResultsFound = false;
      }
      else if (this.filteredResults.length == 0) {
        this.noResultsFound = true;
        this.suggestionWindow = false;
        this.searchResultsWindow = false;
        this.openAccordion = false;
        this.selectedValue = "";
      }
      else {
        this.noResultsFound = false;
        this.suggestionWindow = false;
      }
    }
    else {
      this.noResultsFound = false;
      this.searchResultsWindow = false;
      this.suggestionWindow = false;
      this.openAccordion = false;
      this.filteredResults = [];
      this.selectedValue = "";
    }
  }
  /**
   * Method To get the description code from db and setting it up to accordion
   */
  showDescriptionandInventoryStatus() {
    this.selectedValue = this.rule.inventory_status ? this.rule.inventory_status : "";
    if (this.selectedValue != undefined && this.selectedValue != "") {
      var target = this.selectedValue.toLowerCase();;
      this.filteredResults = this.inventoryStatusDataset.filter(character => {
        return character.cdValName.toLowerCase().includes(target);
      });
      this.statusDescription = this.filteredResults[0]?.cdValLongDesc ? this.filteredResults[0]?.cdValLongDesc : "No Description Available for Selected Status";
      this.openAccordion = true;
    }
  }

  /**
   * populates the data on all forms
  */
  populateRuleDataOnForm(rule) {
    this.rule = rule;
    if (rule?.rule_metadata?.corpus_id) {
      this.multipleCriteriaRule = true;
      this.corpusId = rule.rule_metadata.corpus_id;
    }
    this.rule.inventory_status = rule?.inventory_status;
    this.getDependentDropdownsValues(rule.rule_type);
    this.getDependentDropdownsLtrType(rule.letter_type);
    this.getDependentDropdownsLtrSubType(rule.ltr_rule_sub_type);
    //this.getDependentDropdownLtrOVPDuration(rule.number_of_reminder_letter)
    if (rule.execution_type == "sql_query") {
      this.customSql = rule.conditions[0].query;
      this.showQueryBuilderComponents = false;

      /*Below is taken from create rule code. And this code is useful when user changes from SQL to query builder
      If this code is absent, query builder will load. This is used in create rule as well
      */
      if (Object.keys(this.qbConfig.fields).length) {
        this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[1];
      }
    }
    else if (rule.conditions && rule.conditions.length > 0) {
      this.qbQuery = this.modifyStructureToShowQB(rule.conditions[0]);
    }
    this.getAllJsonFilesData();
    this.retroApply = rule.retro_apply;
    this.bypassApply = rule.bypass_apply;
    this.headerLevel = rule.header_level;
    this.showDescriptionandInventoryStatus();
    this.levelIndicator = rule.rule_level + " Level";
    if (!rule.letter_concept_type) {
      rule.letter_concept_type = 'Single';
    }
    this.ltrWaitDuration.push({ name: this.ltrWaitDurationOVP, value: this.ltrWaitDurationOVP, id: this.ltrWaitDurationOVP })
    if (rule.ltr_rule_sub_type == 'consolidation')
      this.enableInventoryStatus = false;
    this.relationSHJSON = [
      {
        type: 'group',
        name: 'rules',
        label: '',
        column: 1,
        groupControls: [
          {
            type: 'select',
            name: 'rule_type',
            label: 'Rule Type',
            options: this.ruleTypes,
            optionName: 'name',
            optionValue: 'value',
            column: 2,
            closeOnSelect: true,
            id: 'rule_type',
            relationship: this.dependentFieldsData,
            selectedVal: rule.rule_type,
            // 25.1 - disabled: true,
            disabled: this.isInputDisabled,
            required: true,
            placeholder: 'Choose Rule Type'
          },
          {
            type: 'select',
            name: 'letter_type',
            label: 'Letter Type',
            column: 2,
            id: 'letter_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.letterType,
            relationOptions: [],
            relationship: this.dependentLetterData,
            selectedVal: rule.letter_type,
            // 25.1 - disabled: true,
            disabled: this.isInputDisabled,
            required: true,
            visible: this.showField("letter_type", rule.rule_type)
          },
          {
            type: 'select',
            name: 'ltr_rule_sub_type',
            label: 'Rule Subtype',
            column: 2,
            id: 'ltr_rule_sub_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.ltrRuleSubTypes,
            relationOptions: [],
            relationship: this.dependentsubRuleData,
            selectedVal: rule.ltr_rule_sub_type,
            //25.1 - disabled: false,
            disabled: this.isInputDisabled,
            required: rule.rule_type == 'letters' ? this.showFieldLtrType("ltr_rule_sub_type", rule.letter_type) : this.showField("ltr_rule_sub_type", rule.rule_type),
            visible: rule.rule_type == 'letters' ? this.showFieldLtrType("ltr_rule_sub_type", rule.letter_type) : this.showField("ltr_rule_sub_type", rule.rule_type)
          },
          {
            options: this.calculationFields,
            optionName: 'name',
            optionValue: 'value',
            label: 'Calculation Fields',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'calculation_fields',
            column: '2',
            //25.1 - disabled: false,
            disabled: this.isInputDisabled,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("calculation_fields", rule.ltr_rule_sub_type) : this.showField("calculation_fields", rule.rule_type),
            id: 'calculation_fields',
            selectedVal: this.isNull(rule.calculation_fields) ? "" : rule.calculation_fields[0],
            required: true,
            placeholder: 'Choose Calculation Field'
          },
          {
            type: 'select',
            name: 'rule_subtype',
            label: 'Rule Subtype',
            column: 2,
            id: 'rule_subtype',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: this.ruleSubTypes,
            relationOptions: [],
            visible: this.showField("rule_subtype", rule.rule_type),
            disabled: this.isInputDisabled,
            //25.1 - disabled: false,
            selectedVal: rule.rule_subtype,
            required: this.showField('rule_subtype', rule.rule_type),
            placeholder: 'Choose Rule Subtype'
          },
          {
            options: this.reminderLtrCount,
            optionName: 'name',
            optionValue: 'id',
            label: 'Number Of Reminder Letters',
            type: 'select',
            closeOnSelect: true,
            name: 'number_of_reminder_letter',
            column: '2',
            id: 'number_of_reminder_letter',
            required: true,
            // 25.1 - disabled: true,
            disabled: this.isInputDisabled,
            customTags: true,
            selectedVal: rule.number_of_reminder_letter,
            placeholder: 'Choose Reminder Letters',
            relationship: this.dependentsubRuleDurationData,
            relationOptions: [],
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("number_of_reminder_letter", rule.ltr_rule_sub_type) : this.showField("number_of_reminder_letter", rule.rule_type)
          },
          {
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 1(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_in_days',
            column: '2',
            id: 'letter_wait_duration_in_days',
            customTags: true,
            //25.1 - disabled: rule.rule_type == 'letters' ? false : true,
            disabled: this.isInputDisabled,
            required: true,
            options: this.ltrWaitDuration,
            relationOptions: [],
            selectedVal: rule.letter_wait_duration_in_days,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_wait_duration_in_days", rule.ltr_rule_sub_type) : this.showField("letter_wait_duration_in_days", rule.rule_type)
          },
          {
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 2(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_ovp_2',
            column: '2',
            id: 'letter_wait_duration_ovp_2',
            customTags: true,
            //25.1 -  disabled: true,
            disabled: this.isInputDisabled,
            required: true,
            options: this.ltrWaitDurationOvp2,
            relationOptions: [],
            selectedVal: Number(rule.letter_wait_duration_ovp_2),
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubTypeOVPReminder("letter_wait_duration_ovp_2", rule.number_of_reminder_letter) : this.showField("letter_wait_duration_ovp_2", rule.rule_type)
          },
          {
            label: 'Grace Period(in days)',
            type: 'numberSelect',
            name: 'grace_period_in_days',
            column: '2',
            closeOnSelect: true,
            id: 'grace_period_in_days',
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("grace_period_in_days", rule.ltr_rule_sub_type) : this.showField("grace_period_in_days", rule.rule_type),
            //25.1 -  disabled: rule.rule_type == 'letters' ? false : true,
            disabled: this.isInputDisabled,
            required: true,
            minimum: 0,
            maximum: 100,
            value: Number(rule.grace_period_in_days),
            placeholder: 'Choose Grace Period Min 0 Max 100'
          },

          {

            optionName: 'name',
            optionValue: 'value',
            label: 'Type of days',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'type_of_days',
            column: '2',
            id: 'type_of_days',
            options: this.typeOfdays,
            //25.1 - disabled: rule.rule_type == 'letters' ? false : true,
            disabled: this.isInputDisabled,
            required: true,
            selectedVal: rule.type_of_days,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("type_of_days", rule.ltr_rule_sub_type) : this.showField("type_of_days", rule.rule_type),
          },
          {
            options: this.lookBackPeriodValues,
            optionName: 'name',
            optionValue: 'value',
            label: 'Lookback Period (in months)',
            type: 'select',
            closeOnSelect: true,
            name: 'lookup_dates',
            column: '2',
            // 25.1 - disabled: false,
            disabled: this.isInputDisabled,
            visible: this.showField('lookup_dates', rule.rule_type),
            id: 'lookup_dates',
            selectedVal: rule.lookup_dates?.value ? Number(rule.lookup_dates.value) : '',
            required: this.showField('lookup_dates', rule.rule_type),
            placeholder: 'Choose Lookback Period'
          },
          {
            options: this.laggingPeriodValues,
            optionName: 'name',
            optionValue: 'value',
            label: 'Lagging Period (in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'lagging_period',
            column: '2',
            // 25.1 - disabled: false,
            disabled: this.isInputDisabled,
            visible: this.showField('lagging_period', rule.rule_type),
            id: 'lagging_period',
            required: true,
            selectedVal: rule.lagging_period ? Number(rule.lagging_period.value) : '',
            placeholder: 'Choose Lagging Period'
          },
          {
            options: [
              { name: this.selectedProfileClientName, id: this.selectedProfileClientId }
            ],
            optionName: 'name',
            optionValue: 'name',
            label: 'Client Name',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'client_name',
            column: '2',
            //25.1 - disabled: rule.rule_type == 'letters' ? false : true,
            disabled: this.isInputDisabled,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("client_name", rule.ltr_rule_sub_type) : this.showField("client_name", rule.rule_type),
            id: 'client_name',
            required: true,
            selectedVal: rule.client_name,
            placeholder: 'Choose Client Name'
          },
          {
            optionName: 'name',
            optionValue: 'value',
            label: 'Provider',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'provider',
            column: '2',
            //25.1 - disabled: rule.rule_type == 'letters' ? false : true,
            disabled: this.isInputDisabled,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("provider", rule.ltr_rule_sub_type) : this.showField("provider", rule.rule_type),
            id: 'provider',
            required: true,
            options: this.provider,
            selectedVal: rule.provider,
          },
          {

            optionName: 'name',
            optionValue: 'value',
            label: 'Concept',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'letter_concept_type',
            column: '2',
            // 25.1 - disabled: false,
            disabled: this.isInputDisabled,
            required: true,
            options: this.letterConcepts,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("letter_concept_type", rule.ltr_rule_sub_type) : this.showField("letter_concept_type", rule.rule_type),
            id: 'letter_concept_type',
            selectedVal: rule.letter_concept_type,

          },
          {
            label: 'Max no Of Claims Per Letter',
            type: 'numberSelect',
            name: 'max_no_of_claims_per_letter',
            column: '2',
            required: true,
            //25.1 - disabled: rule.rule_type == 'letters' ? false : true,
            disabled: this.isInputDisabled,
            visible: rule.rule_type == 'letters' ? this.showFieldLtrSubType("max_no_of_claims_per_letter", rule.ltr_rule_sub_type) : this.showField("max_no_of_claims_per_letter", rule.rule_type),
            id: 'max_no_of_claims_per_letter',
            minimum: 1,
            maximum: 25,
            value: rule.max_no_of_claims_per_letter,
            placeholder: 'Choose Max No Of Claims Per Letter Min 1 Max 25'
          },
          {
            options: [
              {
                name: 'Audit',
                value: 'audit',
              },
              {
                name: 'Provider Audit',
                value: 'providerAudit',
              },
              {
                name: 'Report Audit',
                value: 'reportAudit',
              },
              {
                name: 'Over Payment',
                value: 'overPayment',
              },
              {
                name: 'Business Audit',
                value: 'businessAudit',
              },
            ],
            optionName: 'name',
            optionValue: 'value',
            label: 'Inventory Type',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'audit_type',
            column: '2',
            // 25.1 - disabled: false,
            disabled: this.isInputDisabled,
            visible: this.showField('audit_type', rule.rule_type),
            id: 'audit_type',
            required: true,
            selectedVal: rule.audit_type,
            placeholder: 'Choose Inventory Type'
          },
          {
            label: 'Release By',
            type: 'date',
            name: 'release_by',
            id: 'release_by',
            column: '2',
            disabled: false,
            visible: this.showField('release_by', rule.rule_type),
            pickerType: 'single',
            required: true,
            dateFormat: 'MM-DD-YYYY',
            minDate: (new Date(rule.release_by) > new Date()) ? Date.now() : this.dateService.getDbgDateFormat(rule.release_by),
            value: this.dateService.getDbgDateFormat(rule.release_by),
            placeholder: 'Choose Date'
          },
        ],
      },

    ];

    this.generalDetailsJson = [
      {
        type: 'group',
        name: 'generalDetailsLeft',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Rule Name',
            type: 'text',
            name: 'rule_name',
            column: '1',
            // 25.1 - disabled: false,
            disabled: this.isInputDisabled,
            value: rule.rule_name,
            required: true,
            placeholder: 'Enter Rule Name'
          },
          {
            label: 'Rule Description',
            type: 'textarea',
            name: 'description',
            id: 'description',
            column: '1',
            disabled: false,
            value: rule.description,
            required: true,
            placeholder: 'Enter Description'
          },
          {
            label: 'Term Reason',
            type: 'text',
            name: 'term_reason',
            column: '1',
            disabled: false,
            value: rule.term_reason,
            placeholder: 'Enter Term Reason'
          },
          // 25.1 - {
          //   label: 'Reason For Edit',
          //   type: 'textarea',
          //   name: 'edit_reason',
          //   column: '1',
          //   disabled: false,
          //   value: rule.edit_reason,
          //   placeholder: ''
          // },
          !this.isDraftRule && {
            label: 'Reason For Edit',
            type: 'textarea',
            name: 'edit_reason',
            column: '1',
            disabled: false,
            value: rule.edit_reason,
            placeholder: ''
          },
        ],
      },
      {
        type: 'group',
        name: 'generalDetailsRight',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Start Date',
            type: 'date',
            name: 'start_date',
            id: 'start_date',
            column: '3',
            disabled: false,
            value: this.dateService.getDbgDateFormat(rule.start_date),
            pickerType: 'single',
            required: true,
            dateFormat: 'MM-DD-YYYY',
            placeholder: 'Enter Date',
            relatedDateControls: [{
              target: 'end_date'
            }]
          },
          {
            label: 'End Date',
            type: 'date',
            name: 'end_date',
            id: 'end_date',
            column: '3',
            disabled: false,
            pickerType: 'single',
            value: this.dateService.getDbgDateFormat(rule.end_date),
            dateFormat: 'MM-DD-YYYY',
            minDate: this.dateService.getFutureDate(rule.start_date, 1, 'MM-dd-YYYY'),
            required: false,
            placeholder: 'Enter Date'
          },
          {
            label: 'Status',
            type: 'text',
            name: 'status',
            column: '3',
            //25.1 - disabled: true,
            disabled: this.isInputDisabled,
            value: String(rule.status) == "true" ? 'Active' : 'Inactive',
          },
          {
            label: 'Review Reminder Date',
            type: 'date',
            name: 'review_remainder_date',
            column: '1',
            disabled: false,
            pickerType: 'single',
            value: rule.review_remainder_date,
            dateFormat: 'MM-DD-YYYY',
            required: true,
            placeholder: 'Enter Date'
          },
          {
            label: 'Business Owner',
            type: 'text',
            name: 'business_owner',
            id: 'business_owner',
            column: '1',
            groupColumn: '1',
            // 25.1 - disabled: false,
            disabled: this.isInputDisabled,
            value: rule.business_owner,
            required: true
          },
          //25.1 - {
          //   label: 'Comments',
          //   type: 'textarea',
          //   name: 'comments',
          //   id: 'comments',
          //   column: '1',
          //   groupColumn: '1',
          //   disabled: false,
          //   value: rule.comments,
          //   required: false
          // }
          !this.isDraftRule && {
            label: 'Comments',
            type: 'textarea',
            name: 'comments',
            id: 'comments',
            column: '1',
            groupColumn: '1',
            disabled: false,
            value: rule.comments,
            required: false
          }
        ],
      },
    ];
    this.additionalDetailsJson = [
      {
        type: 'group',
        name: 'additionalDetailsTop',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'External Point Of Contact',
            group: '',
            type: 'text',
            name: 'external_point_of_contact',
            id: 'external_point_of_contact',
            column: '2',
            groupColumn: '1',
            disabled: false,
            value: rule.external_point_of_contact,
            placeholder: 'Enter External Point Of Contact'
          },
        ],
      },
      {
        type: 'group',
        name: 'additionalDetailsBottom',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'Created By',
            type: 'text',
            name: 'created_by',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: rule.created_by,
          },
          {
            label: 'Created Date',
            type: 'date',
            name: 'created_ts',
            id: 'created_ts',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: this.dateService.getDbgDateFormat(rule.created_ts),
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
          {
            label: 'Updated By',
            type: 'text',
            name: 'updated_by',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: rule.updated_by,
          },
          {
            label: 'Updated Date',
            type: 'date',
            name: 'updated_ts',
            id: 'updated_ts',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: rule['updated date'] ? this.dateService.getDbgDateFormat(rule['updated date']) : this.dateService.getDbgDateFormat(rule['updated_ts']),
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
        ],
      },
    ];

    if (this.isDataReceivedFromSubcription) {
      this.disableRulefields();
    }
    else {
      this.showRuleFieldsOncondition();
    }
    this.showForms = true;
  }

  /**
   * populates objects with all dependent dropdown values
  */
  getDependentDropdownsValues(conditionKey) {
    this.dependentFieldsData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {

            case 'rule_subtype':
              this.ruleSubTypes = dependentField.dataset;
              break;
            case 'letter_type':
              this.letterType = dependentField.dataset;
              break;
            case 'calculation_fields':
              this.calculationFields = dependentField.dataset;
              break;
            case 'lookup_dates':
              this.lookBackPeriodValues = dependentField.dataset;
              this.lookBackPeriodValues.forEach(fieldValue => {
                fieldValue.value = fieldValue.value;
              });
              break;
            case 'lagging_period':
              this.laggingPeriodValues = dependentField.dataset;
              this.laggingPeriodValues.forEach(fieldValue => {
                fieldValue.value = fieldValue.value;
              });
              break;
          }
        });
      }
    });
  }

  getDependentDropdownsLtrType(conditionKey) {
    this.dependentLetterData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'ltr_rule_sub_type':
              this.ltrRuleSubTypes = dependentField.dataset;
              break;
          }
        });
      }
    });
  }

  getDependentDropdownsLtrSubType(conditionKey) {
    this.dependentsubRuleData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'letter_wait_duration_in_days':
              this.ltrWaitDuration = dependentField.dataset;
              this.ltrWaitDurationOvp2 = dependentField.dataset;
              break;
            case 'number_of_reminder_letter':
              this.reminderLtrCount = dependentField.dataset;
              break;
            case 'type_of_days':
              this.typeOfdays = dependentField.dataset;
              break;
            case 'calculation_fields':
              this.calculationFields = dependentField.dataset;
              break;
            case 'provider':
              this.provider = dependentField.dataset;
              break;
            case 'letter_concept_type':
              this.letterConcepts = dependentField.dataset;
              break;
          }
        });
      }
    });
  }
  getDependentDropdownLtrOVPDuration(conditionKey) {
    this.dependentsubRuleDurationData.forEach(element => {
      if (element["when"] == conditionKey && element["updateDataset"] != undefined) {
        element["updateDataset"].forEach(dependentField => {
          switch (dependentField.id) {
            case 'letter_wait_duration_ovp_2':
              this.ltrWaitDurationOvp2 = dependentField.dataset;
              break;
          }
        });
      }
    });
  }
  /**
   * based on ruletype value, some fields will be shown/hidden
   * that is evaluated here
  */
  showField(field, conditionKey) {
    let makeItVisible = true;
    this.dependentFieldsData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });


      }
    });
    return makeItVisible;
  }

  showFieldLtrType(field, conditionKey) {
    let makeItVisible = true;

    this.dependentLetterData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }

  showFieldLtrSubType(field, conditionKey) {
    let makeItVisible = true;
    this.dependentsubRuleData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });
    return makeItVisible;
  }
  showFieldLtrSubTypeOVPReminder(field, conditionKey) {
    let makeItVisible = true;
    this.dependentsubRuleDurationData.forEach(element => {
      if (element["when"] == conditionKey && element["hide"] != undefined) {
        element["hide"].forEach(hideField => {
          if (hideField === field) {
            makeItVisible = false;
          }
        });
      }
    });

    return makeItVisible;
  }

  /**
  * Method to remove pointer function none from QB
  */
  enableQueryBuilder() {
    let queryBuilderDivElement = document.querySelector('div.pad-1rem-cursor');
    queryBuilderDivElement?.classList?.remove('pad-1rem-cursor');
  }

  /**
  * Method fires on segemented selection
  * @param event 
  */
  _onDashboardSGSelection(event: any): void {
    switch (event.selection.label) {
      case STANDARD:
        this.unSelectedIndex = 1;
        this.openConfirmationModal = true;
        this.removeCloseButton();
        break;
      case UPLOAD_FILE:
        setTimeout(() => {
          this.unSelectedIndex = 0;
          this.openConfirmationModal = true;
          this.removeCloseButton();
        }, 50);
        break;
      default:
        break;
    }
  }

  removeCloseButton(): void {
    setTimeout(() => {
      const elements = document.querySelectorAll('marketplace-popup .modal-header .close');
      elements.forEach(element => {
        const htmlEle = element as HTMLElement;
        htmlEle.style.display = 'none';
      });
    }, 0);
  }

  /**
   * modifies selected query builder criterias to the format query builder understands
  */
  modifyStructureToShowQB(qbQuery) {
    const operatorMap = operatorsMapToShowInQb;
    let customFields = [];
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case 'log':
          this.condition = v;
          break;
        case 'conditions':
          this.rules = v;
          break;
        case 'lval':
          this.field = v;
          break;
        case 'rval':
          this.value = v;
          this.startValue = v.start;
          this.endValue = v.end;
          customFields.push(v);
          break;
        case 'op':
          this.operator = operatorMap[v];
          break;
        case 'config':
        case 'operatorList':
        case 'delete':
        case 'json_path':
          delete qbQuery[k];
          break;
        default:
          return v;
      }
    });
    this.pushCustomFieldsToQBConfig(customFields);
    return parsed;
  }

  /**
   * checks for undefined value
  */
  isDefined(fieldValue) {
    if (fieldValue != undefined) return true;
    else return false;
  }
  /**
  * takes to frequently added criteria screen
  */
  AddNewCriteriaOnClick(): void {
    this.router.navigate([
      'product-catalog/rules/create-frequently-used-criteria',
    ]);
  }
  /**
  * takes to dashboard
  */
  returnHomeClick(): void {
    this.router.navigate(['product-catalog/rules']);
  }
  /**
  * populates updated by and updated date
  * method gets called after file upload
  */
  populateAdditionalDetails(updateData) {
    this.additionalDetailsJson = [
      {
        type: 'group',
        name: 'additionalDetailsTop',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'External point of contact',
            group: '',
            type: 'text',
            name: 'external_point_of_contact',
            id: 'external_point_of_contact',
            column: '2',
            groupColumn: '1',
            disabled: false,
            value: this.rule.external_point_of_contact,
          },
        ],
      },
      {
        type: 'group',
        name: 'additionalDetailsBottom',
        label: '',
        column: 1,
        groupControls: [
          {
            label: 'Created by',
            type: 'text',
            name: 'created_by',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: this.rule.created_by,
          },
          {
            label: 'Created date',
            type: 'date',
            name: 'created_ts',
            id: 'created_ts',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: this.dateService.getDbgDateFormat(this.rule.created_ts),
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
          {
            label: 'Updated by',
            type: 'text',
            name: 'updated_by',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: updateData.attached_by,
          },
          {
            label: 'Updated date',
            type: 'date',
            name: 'updated_ts',
            id: 'updated_ts',
            column: '2',
            groupColumn: '1',
            //25.1 - disabled: true
            disabled: this.isInputDisabled,
            value: this.dateService.getDbgDateFormat(updateData['saved date']),
            pickerType: 'single',
            dateFormat: 'MM-DD-YYYY'
          },
        ],
      },
    ];
  }

  /**
   * Method will be called on click of download button 
   * and calls service API to get the file
   */
  DownloadMultiCriteriaFile() {
    this.showLoader = true;
    this.RulesApiService.getMultipleCriteriaFile(this.ruleId, this.corpusId, this.levelIndicator).subscribe(data => {
      this.showLoader = false;
      this.generateExceldata(data, "multi_criteria_file");
    },
      error => {
        this.showLoader = false;
        this.alertService.setErrorNotification({
          notificationHeader: FAIL,
          notificationBody: `${error.statusText}`,
        });
      });
  }

  /**
   * Invoked when excel export icon is clicked
   * @function generateExceldata generates a excel file
   * @param data response from API
   * @param fileName fileName
   * @returns excelFile
   */
  generateExceldata(data: any, fileName: any) {
    if (Object.keys(data).length) {
      let a = document.createElement("a");
      a.id = "excel";
      a.href = "data:text/csv," + data.body;
      a.setAttribute("download", fileName + ".csv");
      document.body.appendChild(a);
      a.download = fileName;
      a.click();
    }
  }

  /* 
  * method evaluating client/concept ids from the event
  * triggered by querybuilder field value change
  */
  getClientConceptValue(event) {
    let fieldChanged = event.rule;
    let fieldValue = event.event.name;
    let fieldValueId = event.event.id;
    if (fieldChanged.field == "CLNT_ID") {
      this.clientIdSelected = fieldValue;
      this.clientIdForECP = fieldValueId;
      this.conceptIdSelected = "";
    } else if (fieldChanged.field == "CNCPT_ID") {
      this.conceptIdSelected = fieldChanged.value;
      this.clientIdSelected = null;
    }
    this.isEdited = true;
  }

  /**
   * Method pushes cusom fields to query Builder config
   */

  pushCustomFieldsToQBConfig(customFields): void {
    this.qbConfig.customFieldList = {};
    this.qbConfig.customFieldList.dataset = [];
    customFields.forEach(column => {
      this.qbConfig.customFieldList.dataset.push({ "name": column, "id": column, "collection": WORKSHEET_HEADERS });
    });
  }

  /**
   * Get all json file data and assign it to respective objevts
   */
  getAllJsonFilesData(): void {
    this.showLoader = true;
    this.isConceptDataReady = false;
    this.RulesApiService.getAssetsJson(constants.RULE_QUERY_SPEC_JSON).subscribe((data) => {
      this.querySpecificationJson = data.sqlStructure;
      if (this.querySpecificationJson && this.querySpecificationJson[0]) {
        this.querySpecificationJson[0].value = this.showQueryBuilderComponents ? "qb" : "custSql";
      }
      let tokenVal = localStorage?.getItem(constants.TOKEN);
      let _clientData = this.clientApiService.getAllClientsInPreferenceCenter();
      let _conceptData = this.conceptApiService.getProductConceptsId(tokenVal);

      forkJoin([_clientData, _conceptData]).subscribe(([clientData, conceptData]) => {
        if (clientData && Array.isArray(clientData)) {
          this.clientData = clientData.map(x => ({ value: x.clientId, name: x.clientName }));
          if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
            const clientControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CLIENTID)[0];
            if (clientControl) clientControl.options = this.clientData;
          }
        }

        if (conceptData && conceptData.executionConceptAnalyticResponse && Array.isArray(conceptData.executionConceptAnalyticResponse)) {
          this.conceptData = conceptData.executionConceptAnalyticResponse
            .filter(x => x.clientId === this.selectedProfileClientId || (this.selectedProfileClientId === ANTM_ID && x.clientId === 0))
            .map(x => ({ id: x.exConceptReferenceNumber, name: x.exConceptReferenceNumber }));
          if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
            const conceptControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0];
            if (conceptControl) conceptControl.options = this.conceptData;
          }
        }
        if (this.rule && this.rule.concept && this.rule.concept.length >= 1) {
          this.compatibleJsonForConcepts = this.rule.concept;
          this.conceptIdSelected = this.rule.concept;
          this.orgnlCnptCarriedfrmLstEdit = this.rule.concept;
          if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
            const rulesLevelControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0];
            const conceptIdControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0];
            if (rulesLevelControl) rulesLevelControl.selectedVal = constants.CONCEPT_LEVEL;
            if (conceptIdControl) conceptIdControl.selectedVal = this.rule.concept;
          }
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0].visible = true;
          //25.1- this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].disabled = false;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].disabled = true;

          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0].disabled = false;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CLIENTID)[0].disabled = false;
        } else if (this.rule.clientId) {
          this.clientIdSelected = this.rule.clientId;
          this.clientIdForECP = this.rule.client;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].selectedVal = constants.CLIENT_LEVEL;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CLIENTID)[0].selectedVal = this.rule.clientId;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CLIENTID)[0].visible = true;
          //25.1 - this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].disabled = false;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].disabled = true;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CLIENTID)[0].disabled = false;
        } else {
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].selectedVal = constants.GLOBAL_LEVEL;
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].disabled = true;
        }
        const selectedRuleLevel = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0].selectedVal;
        if (this.selectedProfileClientId === 59 && selectedRuleLevel !== constants.GLOBAL_LEVEL) {
          this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.PRODUCT)[0].visible = true;
        }
        this.isConceptDataReady = true;
        this.showLoader = false;

      }, error => {
        this.clientData = [];
        this.showLoader = false;
        this.isEdited = this.isDataReceivedFromSubcription ? true : false;
        this.alertService.setErrorNotification({
          notificationHeader: "Error",
          notificationBody: '',
        });
      });

      if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
        const rulesLevelControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0];
        if (rulesLevelControl) rulesLevelControl.disabled = true;
      }
      if (this.querySpecificationJson[0]) {
        this.querySpecificationJson[0].value = this.showQueryBuilderComponents ? "qb" : "custSql";
      }
      // as part of 25.1 below line should be commented
      //this.querySpecificationJson[0].options.map(c => c.enabled = !this.isInputDisabled);

      if (this.querySpecificationJson[0] && this.querySpecificationJson[0].options) {
        this.querySpecificationJson[0].options.map(c => c.enabled = !this.isInputDisabled);
        this.querySpecificationJson[0].disabled = this.isInputDisabled;
      }
      if (this.querySpecificationJson[1] && this.querySpecificationJson[1].groupControls) {
        if (this.querySpecificationJson[1].groupControls[0]) this.querySpecificationJson[1].groupControls[0].disabled = this.isInputDisabled;
        if (this.querySpecificationJson[1].groupControls[1]) this.querySpecificationJson[1].groupControls[1].disabled = this.isInputDisabled;
      }
      if (!this.showQueryBuilderComponents) {
        this.customSqlJson = data.customSQL.filter(c => c.type != "textarea");
        this.customSqlJson[0][constants.GROUP_CONTROLS][0].disabled = this.isInputDisabled;
        if (this.rule.clientId) {
          this.customSqlJson[0][constants.GROUP_CONTROLS][0].selectedVal = constants.CLIENT_LEVEL;
          this.customSqlJson[0][constants.GROUP_CONTROLS][1].options = [{ name: this.rule.client, value: this.rule.clientId }];
          this.customSqlJson[0][constants.GROUP_CONTROLS][1].selectedVal = this.rule.clientId;
          this.customSqlJson[0][constants.GROUP_CONTROLS][1].visible = true;
          this.customSqlJson[0][constants.GROUP_CONTROLS][1].disabled = this.isInputDisabled;
        }
        else if (this.rule.concept) {
          this.customSqlJson[0][constants.GROUP_CONTROLS][0].selectedVal = constants.CONCEPT_LEVEL;
          this.customSqlJson[0][constants.GROUP_CONTROLS][2].visible = true;
          this.customSqlJson[0][constants.GROUP_CONTROLS][2].value = this.rule.concept;
          this.customSqlJson[0][constants.GROUP_CONTROLS][2].disabled = this.isInputDisabled;
        }
      }
      //this.filterOptionbyRuleLevel(this.rule?.rule_level, this.querySpecificationJson);
      setTimeout(() => {
        this.showCustomSqlJson = true;
      }, 0);
    });
    if (this.isDataReceivedFromSubcription) {
      this.disableQueryBuilder();
    }
    else {
      this.enableQueryBuilderOncancel();
    }
  }
  /**
  * Restirct Users to Modify Global level rules, client to concept level change, concept to client level change
 */
  // filterOptionbyRuleLevel(currentRuleLevel: string, configJson: any) {
  //   let ruleslevel = configJson[1].groupControls.filter((x) => x.name == constants.RULES_LEVEL)[0]
  //   switch (currentRuleLevel.trim().toLowerCase()) {
  //     case constants.RULE_LEVEL_GLOBAL:
  //       ruleslevel.disabled = true;
  //       ruleslevel.options = ruleslevel.options.filter(x => x.name.toLowerCase() === constants.RULE_LEVEL_GLOBAL);
  //       break;
  //     case constants.RULE_LEVEL_CONCEPT:
  //     case constants.RULE_LEVEL_CLIENT:
  //       ruleslevel.options = ruleslevel.options.filter(x => x.name.toLowerCase() !== constants.RULE_LEVEL_GLOBAL);
  //       break;
  //   }
  // }
  /**
    * triggers when chip is closed
   */
  closeStateChip(selectedState) {
    this.isEdited = true;
    this.isConceptDataReady = false;
    this.compatibleJsonForConcepts = this.compatibleJsonForConcepts.filter(x => x !== selectedState);
    this.conceptIdSelected = this.compatibleJsonForConcepts;
    this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0].selectedVal = this.compatibleJsonForConcepts;
    setTimeout(() => {
      this.isConceptDataReady = true;
    }, 0);
  }

  /**
    * triggers on change of query spec dynamic form
   */
  mapValuesFromQuerySpecToJson(event): void {
    if (event.current.group.conceptId?.length == 0) {
      this.setStatusOfRuleLevel = true;
    } else {
      this.setStatusOfRuleLevel = false;
    }
    if (event.previous.group.conceptId) {
      (event.current.group.conceptId == event.previous.group.conceptId) ? this.isEdited = (this.isDraftRule || this.isDataReceivedFromSubcription) ? true : false : this.isEdited = true;
    }
    this.querySpecDetailsResponse = event.current;
    this.conceptIdSelected = [];
    let ruleLevel = event.current.group.rulesLevel;
    this.isRuleLevelPresent = ruleLevel == undefined ? false : true;
    switch (ruleLevel) {
      case constants.CONCEPT_LEVEL:
        this.conceptsChanged = true;
        this.conceptIdSelected = event.current.group.conceptId;
        this.levelIndicator = constants.CONCEPT_LEVEL;
        this.compatibleJsonForConcepts = [...this.conceptIdSelected.map((c) => c)];
        this.conceptIdSelected = this.compatibleJsonForConcepts;
        this.clientIdForECP = null;
        this.clientIdSelected = null;
        this.isRuleLevelPresent = true;
        break;
      case constants.GLOBAL_LEVEL:
        this.clientIdForECP = null;
        this.clientIdSelected = null;
        this.levelIndicator = constants.GLOBAL_LEVEL;
        this.conceptIdSelected = [];
        this.isRuleLevelPresent = true;
        break;
      case constants.CLIENT_LEVEL:
        this.levelIndicator = constants.CLIENT_LEVEL;
        this.clientIdSelected = this.clientData.find((clientObj) =>
          clientObj.value == event.current.group.clientId
        )?.value
        this.clientIdForECP = event.current.group.clientId;
        this.conceptIdSelected = [];
        this.compatibleJsonForConcepts = [];
        this.orgnlCnptCarriedfrmLstEdit = [];
        this.isRuleLevelPresent = true;
        break;

      default:
        this.isRuleLevelPresent = false;
        break;
    }
    this.querySpecDetailsFormEvent = event;
    if (this.querySpecDetailsResponse.sqlType == constants.QUERY_BUILDER) {
      this.showQueryBuilderComponents = true;
    }
    else {
      this.showQueryBuilderComponents = false;
    }
    this._onRuleLevelChange(event);
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * Method will be called on custom SQL change
   */
  _onRuleLevelChange(event): void {
    if (event.current.group.rulesLevel == constants.CONCEPT_LEVEL && event.current.group.conceptId) {
      this.conceptIdSelected = event.current.group.conceptId;
    }
  }

  ruleLevelChange(event) {
    this.ruleLevelFormEvent = event;
  }

  closebypassConfirm(): void {
    this.openbypassConfirm = false;
  }
  /**
  * Method will be called on custom SQL change
  */
  _onSqlChange(event): void {
    this.customSql = event;
  }

  //Method to disable rule fields
  disableRulefields() {
    this.relationSHJSON[0]["groupControls"].forEach(element => {
      element["disabled"] = true;
    });

    this.generalDetailsJson.forEach(group => {
      group["groupControls"].forEach(element => {
        element.disabled = true;
      });
    })

    this.additionalDetailsJson.forEach(group => {
      group["groupControls"].forEach(element => {
        element.disabled = true;
      })
    });
    this.isStandardQBSelected = true;
    this.showSubmit = true;
    this.showRuleFieldsOncondition();
  }

  //method to show rule fields based on condition 
  showRuleFieldsOncondition() {
    this.isRetroEnabled = this.isDataReceivedFromSubcription ? false : true;
    this.isBypassEnabled = this.isDataReceivedFromSubcription ? false : true;
    this.showSegmentedControl = this.isDataReceivedFromSubcription ? false : true;
  }

  //Method to disable query builder and form
  disableQueryBuilder() {
    this.showQBuilder = true;
    this.isConceptDataReady = true;
    this.isRuleDef = true;
    setTimeout(() => {
      this.addDisableClass(["marketplace-query-builder", "marketplace-dynamic-form"]);
    }, 100);
  }

  //method to add dynamic class to have pointer events none for form and query builder.
  private addDisableClass(tagNames: string[]) {
    tagNames.forEach(tag => {
      const elements = document.getElementsByTagName(tag);
      if (elements.length) {
        Array.from(elements).forEach(element => element.classList.add("disableQueryBuilder"));
      }
    });
  }

  //Method to enable query builder
  enableQueryBuilderOncancel() {
    const elements = document.getElementsByTagName("marketplace-query-builder");
    if (elements) {
      for (let i = 0; i < elements.length; i++) {
        elements[i].classList.remove("disableQueryBuilder")
      }
    }
    setTimeout(() => {
      this.showQBuilder = true;
      this.isRuleDef = false;
    }, 100);
  }

}
