import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CookieService } from 'ngx-cookie-service';

import { EditComponent } from './edit.component';
import { RulesApiService } from '../_services/rules-api.service';
import { ToastService } from '../../_services/toast.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ProductApiService } from '../../_services/product-api.service';
import { UserManagementApiService } from '../../users/_services/user-management-api.service';
import { AuthService } from '../../_services/authentication.services';

describe('EditComponent - 50%+ Coverage Test', () => {
  let component: EditComponent;
  let fixture: ComponentFixture<EditComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRulesApiService: jasmine.SpyObj<RulesApiService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const rulesApiServiceSpy = jasmine.createSpyObj('RulesApiService', [
      'getAllViewEditRuleAPIs', 'getAssetsJson', 'createEditRule', 'deleteRule',
      'getColumnConfigJsonDuplicate', 'getFileDetailsOfRules', 'uploadFileAndQBCriteria',
      'addFilesToRules', 'getMultipleCriteriaFile', 'getInventoryStatusData'
    ]);
    const toastServiceSpy = jasmine.createSpyObj('ToastService', ['setSuccessNotification', 'setErrorNotification']);
    const utilitiesServiceSpy = jasmine.createSpyObj('UtilitiesService', ['getECPDateFormat']);
    const clientApiServiceSpy = jasmine.createSpyObj('ClientApiService', ['getAllClientsInPreferenceCenter']);
    const productApiServiceSpy = jasmine.createSpyObj('ProductApiService', ['getProductConceptsId']);
    const userManagementApiServiceSpy = jasmine.createSpyObj('UserManagementApiService', ['getAssetsJson']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getStoredUserProfile', 'piAuthorize']);
    const cookieServiceSpy = jasmine.createSpyObj('CookieService', ['get', 'set']);
    
    // Setup service returns
    rulesApiServiceSpy.getInventoryStatusData.and.returnValue(of({
      expiration: 'Expired',
      exception: 'Exception',
      onhold: 'On Hold',
      noRecovery: 'No Recovery',
      lag: 'Lag'
    }));
    rulesApiServiceSpy.getAllViewEditRuleAPIs.and.returnValue(of([
      { status: { code: 200 }, result: { fields: {} } },
      { status: { code: 200 }, result: { metadata: { rules: [{ rule_id: 123, rule_name: 'Test Rule' }] } } }
    ]));
    rulesApiServiceSpy.getAssetsJson.and.returnValue(of({ sqlStructure: [] }));
    rulesApiServiceSpy.createEditRule.and.returnValue(of({ status: { code: 200 } }));
    rulesApiServiceSpy.addFilesToRules.and.returnValue(of({ status: { code: 200 }, result: { uploaded_files: [] } }));

    await TestBed.configureTestingModule({
      declarations: [EditComponent],
      imports: [HttpClientTestingModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: { params: of({ id: '123' }), queryParams: of({ clientId: '1', clientName: 'Test Client' }) } },
        { provide: RulesApiService, useValue: rulesApiServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: UtilitiesService, useValue: utilitiesServiceSpy },
        { provide: ClientApiService, useValue: clientApiServiceSpy },
        { provide: ProductApiService, useValue: productApiServiceSpy },
        { provide: UserManagementApiService, useValue: userManagementApiServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: CookieService, useValue: cookieServiceSpy }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(EditComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRulesApiService = TestBed.inject(RulesApiService) as jasmine.SpyObj<RulesApiService>;

    // Mock sessionStorage
    spyOn(sessionStorage, 'getItem').and.returnValue('TEST_USER');
    spyOn(sessionStorage, 'setItem').and.stub();

    // Mock DOM methods
    spyOn(document, 'getElementById').and.returnValue({
      classList: { add: jasmine.createSpy('add'), remove: jasmine.createSpy('remove') }
    } as any);

    // Initialize component with dateService
    const dateServiceSpy = {
      formatDate: jasmine.createSpy('formatDate').and.returnValue('2023-01-01'),
      getECPDateFormat: jasmine.createSpy('getECPDateFormat').and.returnValue('2023-01-01'),
      getDbgDateFormat: jasmine.createSpy('getDbgDateFormat').and.returnValue('2023-01-01')
    };
    (component as any).dateService = dateServiceSpy;
    
    // Initialize component properties
    component.rule = {
      rule_id: 123,
      rule_name: 'Test Rule',
      rule_type: 'Exclusion',
      rule_subtype: 'Test Subtype',
      start_date: new Date(),
      end_date: new Date(),
      inventory_status: 'Active',
      rule_level: 'Global',
      is_draft: true,
      retro_apply: false,
      bypass_apply: false,
      header_level: false
    };
    component.ruleId = 123;
    component.userId = 'TEST_USER';
    component.selectedValue = 'active';
    component.levelIndicator = 'Global Level';
    component.inventoryStatusOptions = {
      expiration: 'Expired',
      exception: 'Exception',
      onhold: 'On Hold',
      noRecovery: 'No Recovery',
      lag: 'Lag'
    };
    component.inventoryStatusDataset = [
      { value: 'expiration', label: 'Expired' },
      { value: 'exception', label: 'Exception' },
      { value: 'onhold', label: 'On Hold' }
    ];
    component.relationSHJSON = [{ groupControls: [] }];
    component.qbConfig = { customFieldList: { dataset: [] }, fields: {} };
    component.dependentFieldsData = [];
    component.dependentLetterData = [];
    component.dependentsubRuleData = [];
    component.dependentsubRuleDurationData = [];
    component.compatibleJsonForConcepts = [];
    component.filteredResults = [];
    component.fileUploadEditJSON = [];
    component.postUploadDataJson = { commentsInUpload: '' };
    component.additionalDetailsJson = [];
    component.mainFormValuesAfterEdit = [];
    component.generalDetailsResponse = [];
    component.additionalDetailsResponse = [];
    component.addedConcepts = [];
    component.deletedConcepts = [];
    component.sgDashboardDataset = [];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should test basic utility methods', () => {
    expect(component.isDefined('test')).toBe(true);
    expect(component.isDefined(null)).toBe(false);
    expect(component.isNull(null)).toBe(true);
    expect(component.isNull('test')).toBe(false);
  });

  it('should test navigation methods', () => {
    component.cancelEdit();
    expect(mockRouter.navigate).toHaveBeenCalled();
    
    const mockEvent = { selected: { url: '/test-url' } };
    component.breadcrumSelection(mockEvent);
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/test-url']);
  });

  it('should test modal methods', () => {
    component.editSubmitOpenModel = true;
    component.displayStyle = 'block';
    component.closePopup();
    expect(component.editSubmitOpenModel).toBe(false);
    expect(component.displayStyle).toBe('none');

    component.showConfirmSubmitModal = true;
    component.cancelSubmission();
    expect(component.showConfirmSubmitModal).toBe(false);

    component.conceptsChangedPopUp = true;
    component.conceptsChangedPopUpClose();
    expect(component.conceptsChangedPopUp).toBe(false);

    component.openImpactReportPopup = true;
    component.savedConfirmPopupClose();
    expect(component.openImpactReportPopup).toBe(false);

    component.openFileUploadConfirmModal = true;
    component.closeFileUploadModal();
    expect(component.openFileUploadConfirmModal).toBe(false);

    component.openbypassConfirm = true;
    component.closebypassConfirm();
    expect(component.openbypassConfirm).toBe(false);
  });

  it('should test form state methods', () => {
    const mockEvent = { toggle: true };
    component.setRetro(mockEvent);
    expect(component.rule['retro_apply']).toBe(true);
    expect(component.isEdited).toBe(true);
    
    component.setBypass({ toggle: false });
    expect(component.rule['bypass_apply']).toBe(false);

    component.setLevel({ toggle: true });
    expect(component.rule['header_level']).toBe(true);
  });

  it('should test file upload methods', () => {
    component.uploadFileInEditRule();
    expect(component.fileDetailsExcelOpenModel).toBe(true);
    expect(component.isFileReady).toBe(true);
    expect(component.fileUploadPopup).toBe('block');

    component.fileUploadpopUpReset();
    expect(component.isFileReady).toBe(false);
    expect(component.isTextReady).toBe(false);
    expect(component.fileUploadPopup).toBe('none');

    component.closePopupUploadForEditRule();
    expect(component.fileUploadPopup).toBe('none');

    component.onSubmitSkipClickedEitRule();
    expect(component.fileUploadPopup).toBe('none');
  });

  it('should test validation methods', () => {
    component.levelIndicator = 'Global Level';
    component.validateEdit();
    expect(component.showMessage).toBe(true);
    expect(component.displayStyle).toBe('block');
    
    component.checkForDuplicateRules();
    expect(component.editSubmitOpenModel).toBe(true);
    expect(component.showMessage).toBe(false);
    expect(component.displayDuplicateMessage).toBe(true);
  });

  it('should test query builder methods', () => {
    const mockEvent = { field: 'test_field' };
    component.qbFieldChange(mockEvent);
    expect(component.isEdited).toBe(true);

    component.showQBuilder = true;
    component.clearQB();
    expect(component.showQBuilder).toBe(false);

    expect(() => component.dropRecentList({})).not.toThrow();
    expect(() => component.qbChange({})).not.toThrow();
    expect(() => component.enableQueryBuilder()).not.toThrow();
  });

  it('should test data mapping methods', () => {
    const mockEvent = { value: { comments: 'Test comment' } };
    component.mapValuesToUploadJson(mockEvent);
    expect(component.postUploadDataJson.commentsInUpload).toBe('Test comment');
    
    const mockTabEvent = { name: 'Rule History' };
    component.onTabSelection(mockTabEvent);
    expect(component.showHistory).toBe(true);
    expect(component.selectedTabIndex).toBe(1);
  });

  it('should test breadcrumb dataset', () => {
    expect(component.breadcrumbDataset).toEqual([
      { label: 'Home', url: '/' },
      { label: 'Rules engine', url: '/rules' },
      { label: 'Edit rule' }
    ]);
  });

  it('should test API methods', () => {
    component.rule = {
      rule_name: 'Test Rule',
      rule_type: 'Exclusion',
      rule_subtype: 'Test Subtype',
      start_date: new Date(),
      end_date: new Date()
    };
    component.selectedValue = 'active';
    
    component.editRule();
    expect(mockRulesApiService.createEditRule).toHaveBeenCalled();
  });

  it('should test additional methods for coverage', () => {
    component.checkForDuplicateRules();
    expect(component.editSubmitOpenModel).toBe(true);
    
    component.setLevel({ toggle: true });
    expect(component.rule['header_level']).toBe(true);
    
    component.closeConfirmationModal();
    expect(component.showSegmentedControl).toBe(false);
    
    const mockFile = new File(['test'], 'test.csv');
    const mockEvent = { target: { files: [mockFile] } } as any;
    spyOn(component, 'validateMaxFileSize').and.returnValue(false);
    spyOn(component, 'checkValidationForUploadFile').and.stub();
    
    component.upload(mockEvent);
    expect(component.fileUploadEditJSON).toEqual(mockEvent);
  });

  it('should test more coverage methods', () => {
    component.fileUploadEditJSON = [new File(['test'], 'test.csv')];
    component.postUploadDataJson = { commentsInUpload: 'Test comment' };
    component.showMaxLimitMsg = false;
    component.checkValidationForUploadFile();
    expect(component.isDisabled).toBe(false);
    
    const mockCustomFields = ['field1', 'field2'];
    component.pushCustomFieldsToQBConfig(mockCustomFields);
    expect(component.qbConfig.customFieldList.dataset.length).toBe(2);
    
    component.generatePreview();
    expect(mockRouter.navigate).toHaveBeenCalled();
  });

  it('should test event handlers', () => {
    const mockEvent = new Event('click');
    expect(() => component.moveToOptionSelected(mockEvent)).not.toThrow();
    
    const mockTableEvent = { ready: true };
    expect(() => component.tableReady(mockTableEvent)).not.toThrow();
    
    const mockCellEvent = new Event('change');
    expect(() => component.cellValueChanged(mockCellEvent)).not.toThrow();
  });

  it('should test form submission methods', () => {
    component.showConfirmSubmitModal = true;
    spyOn(component, 'editRule').and.stub();
    
    component.SubmitConfirm();
    expect(component.showConfirmSubmitModal).toBe(false);
    expect(component.editRule).toHaveBeenCalled();
  });

  it('should test additional coverage scenarios', () => {
    expect(component.getAllJsonFilesData).toBeDefined();
    expect(component.callGetRuleApis).toBeDefined();
    expect(component.getConfigForDuplicateRules).toBeDefined();
    
    const mockQbQuery = {
      condition: 'and',
      rules: [{ field: 'test', operator: 'Equal', value: 'test' }]
    };
    const result = component.modifyQBuilderStructure(mockQbQuery);
    expect(result.log).toBe('and');
  });

  it('should test navigation routing methods', () => {
    component.AddNewCriteriaOnClick();
    expect(mockRouter.navigate).toHaveBeenCalledWith([
      'product-catalog/rules/create-frequently-used-criteria'
    ]);

    component.returnHomeClick();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules']);
  });

  it('should test data processing methods', () => {
    const array1 = ['concept1', 'concept2', 'concept3'];
    const array2 = ['concept2', 'concept4', 'concept5'];
    const result = component.symmetricDiffOfConcetps(array1, array2);
    expect(result).toEqual(['concept1', 'concept3', 'concept4', 'concept5']);

    component.showConceptsChanges();
    expect(component.addedConcepts).toEqual([]);
    expect(component.deletedConcepts).toEqual([]);
  });

  it('should test segmented control methods', () => {
    const mockSGEvent = {
      selection: { label: 'Standard' }
    };
    component.unSelectedIndex = 0;
    component._onDashboardSGSelection(mockSGEvent);
    expect(component.unSelectedIndex).toBe(1);

    const mockRuleLevelEvent = { level: 'Global' };
    component.ruleLevelChange(mockRuleLevelEvent);
    expect(component.ruleLevelFormEvent).toEqual(mockRuleLevelEvent);

    const mockSqlEvent = 'SELECT * FROM test';
    component._onSqlChange(mockSqlEvent);
    expect(component.customSql).toBe(mockSqlEvent);
  });

  it('should test field visibility methods', () => {
    const isVisible = component.showField('field1', 'testCondition');
    expect(typeof isVisible).toBe('boolean');

    expect(() => component.getDependentDropdownsValues('testKey')).not.toThrow();

    const isLtrVisible = component.showFieldLtrType('field1', 'condition');
    expect(typeof isLtrVisible).toBe('boolean');

    const isSubTypeVisible = component.showFieldLtrSubType('field1', 'condition');
    expect(typeof isSubTypeVisible).toBe('boolean');

    const isOVPVisible = component.showFieldLtrSubTypeOVPReminder('field1', 'condition');
    expect(typeof isOVPVisible).toBe('boolean');
  });

  it('should test inventory status selection', () => {
    const mockItem = {
      cdValLongDesc: 'Test Description',
      cdValShrtDesc: 'Test Short'
    };
    component.onSelect(mockItem);
    expect(component.statusDescription).toBe('Test Description');
    expect(component.statusSuggestion).toBe('Test Short');
    expect(component.isEdited).toBe(true);
  });

  it('should test component state management', () => {
    component.isDataReceivedFromSubcription = false;
    component.showRuleFieldsOncondition();
    expect(component.isRetroEnabled).toBe(true);
    expect(component.isBypassEnabled).toBe(true);
    expect(component.showSegmentedControl).toBe(true);

    component.showQBuilder = false;
    component.disableQueryBuilder();
    expect(component.showQBuilder).toBe(true);
    expect(component.isConceptDataReady).toBe(true);
    expect(component.isRuleDef).toBe(true);
  });

  it('should test inventory status and filtering methods', () => {
    // Test getInventoryStatusData
    component.getInventoryStatusData();
    expect(mockRulesApiService.getInventoryStatusData).toHaveBeenCalled();

    // Test showDescriptionandInventoryStatus
    component.rule = { inventory_status: 'expired' };
    component.inventoryStatusDataset = [
      { value: 'expired', label: 'Expired Item', description: 'Item has expired' }
    ];
    component.showDescriptionandInventoryStatus();
    expect(component.selectedValue).toBe('expired');

    // Test inventoryInputfocusOut
    component.filteredResults = [];
    component.selectedValue = 'test';
    component.inventoryInputfocusOut({});
    expect(component.selectedValue).toBeDefined();

    // Test giveDescriptionForStatus with proper setup
    component.inventoryStatusDataset = [
      { value: 'expired', label: 'Expired Item' }
    ];
    const mockEvent = { target: { value: 'exp' } };
    component.giveDescriptionForStatus(mockEvent);
    expect(component.filteredResults).toBeDefined();
  });

  it('should test file validation and upload methods', () => {
    // Test validateMaxFileSize
    component.fileUploadEditJSON = [
      new File(['small content'], 'small.csv', { type: 'text/csv' })
    ];
    const isMaxedOut = component.validateMaxFileSize();
    expect(typeof isMaxedOut).toBe('boolean');

    // Test onSubmitUploadClickedEditRule
    component.isLoading = false;
    component.postUploadDataJson = { commentsInUpload: 'Test comment' };
    component.onSubmitUploadClickedEditRule();
    expect(component.isLoading).toBe(true);

    // Test multipleCriteriaFileUpload
    component.qbQuery = {
      condition: 'and',
      rules: [{ value: 'test', field: 'test', operator: 'Equal', static: false, active: true }]
    };
    component.qbFilled = true;
    component.multiCriteriaFile = { 0: new File(['test'], 'test.csv') };
    component.corpusId = '';
    component.levelIndicator = 'Global Level';
    mockRulesApiService.uploadFileAndQBCriteria.and.returnValue(of({
      result: { uploaded_files: [{ corpus_id: 'test-corpus-id' }] }
    }));
    spyOn(component, 'recursiveFuncForCheckingEmptyField').and.stub();
    spyOn(component, 'modifyQBuilderStructure').and.returnValue({});

    component.multipleCriteriaFileUpload();
    expect(mockRulesApiService.uploadFileAndQBCriteria).toHaveBeenCalled();
  });

  it('should test form mapping and data processing methods', () => {
    // Test mapValuesFromMainToJson
    const mockMainEvent = {
      controls: [
        { name: 'rule_name', value: 'Test Rule Main' },
        { name: 'rule_type', value: 'Exclusion' }
      ]
    };
    component.mapValuesFromMainToJson(mockMainEvent);
    expect(component.mainDetailsResponse).toEqual(mockMainEvent.controls);
    expect(component.isEdited).toBe(true);

    // Test mapValuesFromGeneralToJson
    const mockGeneralEvent = {
      controls: [
        { name: 'start_date', value: '2023-01-01' },
        { name: 'end_date', value: '2023-12-31' }
      ]
    };
    component.mapValuesFromGeneralToJson(mockGeneralEvent);
    expect(component.generalDetailsResponse).toEqual(mockGeneralEvent.controls);

    // Test mapValuesFromAdditionalToJson
    const mockAdditionalEvent = {
      controls: [
        { name: 'comments', value: 'Additional test comment' }
      ]
    };
    component.mapValuesFromAdditionalToJson(mockAdditionalEvent);
    expect(component.additionalDetailsResponse).toEqual(mockAdditionalEvent.controls);

    // Test handleChildClick
    const mockChildEvent = {
      button: 'Cancel',
      name: 'Test'
    };
    spyOn(component, 'onTabSelection').and.stub();
    component.handleChildClick(mockChildEvent);
    expect(mockChildEvent.name).toBe('Edit Rule');
    expect(component.onTabSelection).toHaveBeenCalled();
  });

  it('should test query builder configuration and structure methods', () => {
    // Test modifyStructureToShowQB
    const mockQbQuery = {
      condition: 'and',
      rules: [
        { field: 'test_field', operator: 'Equal', value: 'test_value' }
      ]
    };
    const result = component.modifyStructureToShowQB(mockQbQuery);
    expect(result).toBeDefined();
    expect(result.log).toBe('and');

    // Test modifyQBConfig
    const mockMasterData = {
      CLNT_ID: { type: 'string', label: 'Client ID' },
      CNCPT_ID: { type: 'number', label: 'Concept ID' }
    };
    component.modifyQBConfig(mockMasterData);
    expect(component.qbConfig.fields).toBeDefined();

    // Test recursiveFuncForCheckingEmptyField
    const mockRules = [
      { value: 'test_value', field: 'test_field' },
      { value: '', field: 'empty_field' }
    ];
    component.recursiveFuncForCheckingEmptyField(mockRules);
    expect(component.qbFilled).toBeDefined();
  });

  it('should test dependent dropdown and field visibility methods', () => {
    // Test getDependentDropdownsLtrType
    component.dependentLetterData = [
      {
        when: 'testCondition',
        updateDataset: [
          { field: 'letter_type', dataset: ['type1', 'type2'] }
        ]
      }
    ];
    expect(() => component.getDependentDropdownsLtrType('testCondition')).not.toThrow();

    // Test getDependentDropdownsLtrSubType
    component.dependentsubRuleData = [
      {
        when: 'testCondition',
        updateDataset: [
          { field: 'sub_type', dataset: ['sub1', 'sub2'] }
        ]
      }
    ];
    expect(() => component.getDependentDropdownsLtrSubType('testCondition')).not.toThrow();

    // Test getDependentDropdownLtrOVPDuration
    component.dependentsubRuleDurationData = [
      {
        when: 'testCondition',
        updateDataset: [
          { field: 'duration', dataset: ['30', '60'] }
        ]
      }
    ];
    expect(() => component.getDependentDropdownLtrOVPDuration('testCondition')).not.toThrow();
  });

  it('should test client and concept selection methods', () => {
    // Test getClientConceptValue
    const mockClientEvent = {
      rule: { field: 'CLNT_ID', value: 'client123' },
      event: { name: 'Test Client', id: 456 }
    };
    component.getClientConceptValue(mockClientEvent);
    expect(component.clientIdSelected).toBe(456);
    expect(component.clientIdForECP).toBe(456);

    // Test closeStateChip
    component.compatibleJsonForConcepts = ['state1', 'state2', 'state3'];
    component.isConceptDataReady = true;
    component.closeStateChip('state2');
    expect(component.compatibleJsonForConcepts).toEqual(['state1', 'state3']);
    expect(component.isEdited).toBe(true);
    expect(component.isConceptDataReady).toBe(false);

    // Test mapValuesFromQuerySpecToJson
    const mockQuerySpecEvent = {
      current: {
        group: {
          conceptId: ['concept1', 'concept2'],
          rulesLevel: 'Concept'
        }
      }
    };
    component.setStatusOfRuleLevel = false;
    component.mapValuesFromQuerySpecToJson(mockQuerySpecEvent);
    expect(component.conceptIdSelected).toEqual(['concept1', 'concept2']);

    // Test _onRuleLevelChange
    const mockRuleLevelEvent = {
      current: {
        group: {
          rulesLevel: 'Concept Level',
          conceptId: ['concept123']
        }
      }
    };
    component._onRuleLevelChange(mockRuleLevelEvent);
    expect(component.conceptIdSelected).toEqual(['concept123']);
  });

  it('should test file download and generation methods', () => {
    // Test DownloadMultiCriteriaFile
    component.ruleId = 123;
    component.corpusId = 'test-corpus';
    component.levelIndicator = 'Global';
    component.showLoader = false;
    mockRulesApiService.getMultipleCriteriaFile.and.returnValue(of({ body: 'csv,data' }) as any);
    spyOn(component, 'generateExceldata').and.stub();

    component.DownloadMultiCriteriaFile();
    expect(component.showLoader).toBe(true);
    expect(mockRulesApiService.getMultipleCriteriaFile).toHaveBeenCalledWith(123, 'test-corpus', 'Global');

    // Test generateExceldata
    const mockData = { body: 'test,csv,data' };
    const mockAnchor = {
      id: '',
      href: '',
      download: '',
      click: jasmine.createSpy('click')
    };
    spyOn(document, 'createElement').and.returnValue(mockAnchor as any);
    spyOn(document.body, 'appendChild').and.stub();
    spyOn(document.body, 'removeChild').and.stub();
    spyOn(window.URL, 'createObjectURL').and.returnValue('blob:url');

    component.generateExceldata(mockData, 'test_file');
    expect(document.createElement).toHaveBeenCalledWith('a');
    expect(mockAnchor.click).toHaveBeenCalled();
  });

  it('should test validation and error handling methods', () => {
    // Test validateEditDynamicForms
    component.relationSHJSON = [
      {
        groupControls: [
          { name: 'field1', visible: true, value: '', required: true },
          { name: 'field2', visible: true, value: 'test', required: true }
        ]
      }
    ];
    component.mainFieldsNullCheckCount = 5;
    component.validateEditDynamicForms('save');
    expect(component.mainFieldsNullCheckCount).toBe(0);

    // Test showAllInvalidFields
    component.editErrOpenModel = false;
    spyOn(component, 'resetValidFields').and.stub();
    spyOn(document, 'querySelectorAll').and.returnValue([] as any);

    component.showAllInvalidFields();
    expect(component.editErrOpenModel).toBe(true);
    expect(component.resetValidFields).toHaveBeenCalled();

    // Test resetValidFields
    spyOn(document, 'querySelectorAll').and.returnValue([
      { classList: { remove: jasmine.createSpy('remove') } }
    ] as any);
    component.resetValidFields();
    expect(document.querySelectorAll).toHaveBeenCalled();
  });

  it('should test component lifecycle and initialization methods', () => {
    // Test ngOnInit
    Object.defineProperty(mockRouter, 'url', {
      get: () => '/rules/edit/456'
    });

    component.ngOnInit();
    expect(component.ruleId).toBe(456);
    expect(component.headerText).toBe('Edit Rule 456');

    // Test populateAdditionalDetails
    const mockUpdateData = {
      files: [
        {
          file_name: 'test.csv',
          file_size: 1024,
          upload_date: '2023-01-01',
          comments: 'Test file'
        }
      ]
    };
    component.populateAdditionalDetails(mockUpdateData);
    expect(component.additionalDetailsJson).toBeDefined();
    expect(component.additionalDetailsJson.length).toBeGreaterThan(0);

    // Test callGetFileDetailsRules
    const mockFileDetails = {
      status: { code: 200 },
      result: {
        files: [
          { file_name: 'test.csv', file_size: 1024 }
        ]
      }
    };
    mockRulesApiService.getFileDetailsOfRules.and.returnValue(of(mockFileDetails));
    spyOn(component, 'populateAdditionalDetails').and.stub();

    component.callGetFileDetailsRules('Global', 1);
    expect(mockRulesApiService.getFileDetailsOfRules).toHaveBeenCalledWith(456, 'Global', 1);
  });

  it('should test rule deletion and discard methods', () => {
    // Test discardSavedRule
    component.rule = {
      rule_name: 'Test Rule',
      rule_id: 123,
      is_draft: true,
      rule_level: 'Global'
    };
    component.isEditedRule = false;
    component.showLoader = false;
    mockRulesApiService.deleteRule.and.returnValue(of({
      status: { code: 200 },
      result: { metadata: { deleted: true, rule_id: 123 } }
    }));

    component.discardSavedRule();
    expect(mockRulesApiService.deleteRule).toHaveBeenCalled();
    expect(component.showLoader).toBe(true);
  });

  it('should test DOM manipulation and UI methods', () => {
    // Test removeFileParserTable
    const mockTableDiv = { classList: { add: jasmine.createSpy('add') } };
    const mockQueryBuilderDiv = { classList: { add: jasmine.createSpy('add') } };
    spyOn(document, 'querySelector').and.returnValues(mockTableDiv as any, mockQueryBuilderDiv as any);

    component.removeFileParserTable();
    expect(document.querySelector).toHaveBeenCalledWith('div.sheetsData-container');
    expect(document.querySelector).toHaveBeenCalledWith('div.pad-1rem');

    // Test removeCloseButton
    const mockElements = [
      { style: { display: '' } },
      { style: { display: '' } }
    ];
    spyOn(document, 'querySelectorAll').and.returnValue(mockElements as any);

    expect(() => component.removeCloseButton()).not.toThrow();

    // Test enableQueryBuilderOncancel
    const mockQBElements = [
      { style: { pointerEvents: '' } },
      { style: { pointerEvents: '' } }
    ];
    spyOn(document, 'getElementsByTagName').and.returnValue(mockQBElements as any);

    expect(() => component.enableQueryBuilderOncancel()).not.toThrow();
  });
});
