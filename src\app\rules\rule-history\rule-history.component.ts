import { Component, OnInit, ViewEncapsulation, Input, Output, EventEmitter } from '@angular/core';
import { RulesApiService } from '../_services/rules-api.service';
import { DatePipe } from '@angular/common';
import { switchMap, map, catchError } from 'rxjs/operators';
import { of, forkJoin } from 'rxjs';
import { OperatorsRulesQB, operatorsMapToShowInQb } from '../_services/Rules-QB-Constants';

const WORKSHEET_HEADERS = "Worksheet Headers";
@Component({
  selector: 'app-rule-history',
  templateUrl: './rule-history.component.html',
  styleUrls: ['./rule-history.component.css'],
  // encapsulation: ViewEncapsulation.None,
  providers: [DatePipe]
})
export class RuleHistoryComponent implements OnInit {

  showReinstate: boolean = false;
  @Input() ruleId: number;
  @Input() ruleLevel: string = "";
  @Input() screenName: string = "";

  @Output() buttonClicked: EventEmitter<Object> = new EventEmitter<Object>();
  openPanelActiveIndex: string = '0';
  reInstateRuleVersionDetails: any;
  showLoader = false;
  ruleUpdatedDateTime: any;

  editundoSVG = `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M5.80747 3.03051C5.53038 2.72263 5.05616 2.69767 4.74828 2.97476L2.24828 5.22476L2.17241 5.3036C1.91954 5.60767 1.94483 6.0666 2.24828 6.3397L4.74828 8.5897L4.8361 8.6578C5.14075 8.85992 5.55557 8.81385 5.80747 8.53396L5.87557 8.44614C6.07769 8.14148 6.03162 7.72667 5.75172 7.47476L4.70384 6.53223H9.125L9.31018 6.53723C11.088 6.63336 12.5 8.1054 12.5 9.90723C12.5 11.7712 10.989 13.2822 9.125 13.2822H6.75L6.64823 13.2891C6.28215 13.3387 6 13.6525 6 14.0322C6 14.4464 6.33579 14.7822 6.75 14.7822H9.125L9.34215 14.7775C11.9338 14.6639 14 12.5269 14 9.90723C14 7.21484 11.8174 5.03223 9.125 5.03223H4.70384L5.75172 4.0897L5.82866 4.00952C6.06165 3.72776 6.05937 3.3104 5.80747 3.03051Z" fill="#231E33"/>
  </svg>`
  rulesHistoryData: any[] = [];
  displayedRecords = [];
  currentIndex = 0;
  recordsTobeShown = 5;
  switchToggleNames: any = { 'onText': 'Value', 'offText': 'CFF' };
  operators: any = OperatorsRulesQB;
  qbConfig: any = {
    fields: {
      conceptID: {
        name: 'ConceptID',
        type: 'numeric',
        mutuallyExclusive: ['client'],
      },
      memberID: { name: 'MemeberID', type: 'text' },
      DOB: { name: 'DOB', type: 'calendar' },
      market: {
        name: 'Market',
        type: 'multipleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      country: {
        name: 'Country',
        type: 'singleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      age: { name: 'age', type: 'numeric', regPattern: '^\\d+$', regexMsg: 'Enter only positive numbers', forceRegex: true },
      client: {
        name: 'client',
        type: 'text',
        mutuallyExclusive: ['conceptID'],
      },
    },
    validations: {
      unique: ['CLNT_ID', 'CNCPT_ID']
    }
  };
  showAccordian: boolean = false;
  constructor(
    private rulesAPIService: RulesApiService,
    private datePipe: DatePipe
  ) { }

  ngOnInit(): void {
    this.showLoader = true;
    this.rulesAPIService.getRuleHistoryData(this.ruleId, this.ruleLevel).pipe(
      switchMap((ruleHistoryData: any) => {
        if (ruleHistoryData && ruleHistoryData.status.success) {
          const _ruleHistoryAPIData = of(ruleHistoryData.result.metadata.rules);
          const _userNamesForClient = this.rulesAPIService.getUserNameForClient();
          const _masterDataFromAPI = this.rulesAPIService.getMasterData();
          return forkJoin([_ruleHistoryAPIData, _userNamesForClient, _masterDataFromAPI]);
        }
        return of(null);
      }),
      catchError(error => {
        console.error('Error in API 1:', error);
        this.showLoader = true;
        return of(null); // Handle the error and return a fallback value
      })
    ).subscribe(
      modifiedRulesHistoryData => {
        if (!modifiedRulesHistoryData) {
          this.showLoader = false;
          return;
        }
        this.rulesHistoryData = modifiedRulesHistoryData[0].map((ruleHistory) => {
          let editedBy = ruleHistory?.updated_by?.toUpperCase() ? ruleHistory?.updated_by?.toUpperCase() : ruleHistory?.created_by?.toUpperCase()
          const userObj = modifiedRulesHistoryData[1].find(user => user?.userId?.toUpperCase() === editedBy);
          let createdUpdatedDetails = ruleHistory.updated_ts ? ruleHistory.updated_ts : ruleHistory.created_ts
          return {
            ...ruleHistory,
            headerString: this.formatDate(createdUpdatedDetails, ruleHistory.version_seq),
            subText: ruleHistory.version_seq == modifiedRulesHistoryData[0].length ?
              `<span><strong>Edited By: </strong>${userObj?.userName ? userObj?.userName : ""} <span class="headerbadge badge badge-pill badge-active">Active Version</span> </span>` :
              `<span><strong>Edited By: </strong>${userObj?.userName ? userObj?.userName : ""}</span>`,
            concept: ruleHistory.concept ? JSON.parse(ruleHistory.concept.replace(/'/g, '"')).join(', ') : '-',
            calculation_fields: ruleHistory.calculation_fields ? ruleHistory.calculation_fields[0] : '-',
            qbQuery: ruleHistory?.execution_type == "sql_query" ? ruleHistory.conditions[0].query : this.modifyStructureToShowQB(ruleHistory.conditions[0]),
          }
        });
        if (modifiedRulesHistoryData[2] && modifiedRulesHistoryData[2].result && modifiedRulesHistoryData[2].result.fields) {
          let masterData = modifiedRulesHistoryData[2].result.fields;
          this.qbConfig.fields = this.modifyQBConfig(masterData['query_fields']);
        }
        this.displayNextRecords();
        setTimeout(() => {
          this.showAccordian = true
          this.showLoader = false;
        }, 100);
      }, error => console.log(error))
  }

  get loadMoreRecords() {
    const remainingRecords = this.rulesHistoryData.length - this.currentIndex;
    const recordsTobeLoad = Math.min(this.recordsTobeShown, remainingRecords);
    const nextRecords = this.rulesHistoryData.slice(this.currentIndex, this.currentIndex + recordsTobeLoad);
    return nextRecords;
  }

  displayNextRecords(): void {
    this.showAccordian = false
    const nextRecords = this.loadMoreRecords;
    this.displayedRecords = [...this.displayedRecords, ...nextRecords];
    this.currentIndex += nextRecords.length;
    setTimeout(() => {
      this.showAccordian = true
    }, 100);
  }

  hasMoreRecords(): boolean {
    return this.rulesHistoryData ? this.currentIndex < this.rulesHistoryData.length : false;
  }

  displayShowLessBtn(): boolean {
    return this.rulesHistoryData ? this.rulesHistoryData.length > 5 && !this.hasMoreRecords() : false;
  }

  resetToInitialRecords(): void {
    this.displayedRecords = this.rulesHistoryData.slice(0, this.recordsTobeShown);
    this.currentIndex = this.recordsTobeShown;
  }

  get displayRuleHIstoryRecords() {
    return this.displayedRecords;
  }

  formatDate(dateString: string, versionSeq: string): string {
    if (dateString) {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return `<div class="col-10 d-flex">
                  <strong class="pr-1">Invalid Date</strong> &nbsp |
                  <span class="timepart">Invalid Time</span> &nbsp
                  <span class="headerbadge badge badge-pill badge-light"> Version ${versionSeq}</span>`;
      }
      const datePart = this.datePipe.transform(date, 'MMMM d, y');
      const timePart = this.datePipe.transform(date, 'h:mm:ss a');
      return `<div class="col-10 d-flex"> 
                <strong class="pr-1">${datePart}</strong> &nbsp |
                <span class="timepart">${timePart} </span> &nbsp 
                <span class="headerbadge badge badge-pill badge-light"> Version ${versionSeq}</span> 
              </div>`;
    }
    return '';
  }

  closePopup(event) {
    this.showReinstate = false;
  }

  /**
  * Method to show reinstate popup
 */
  showReinstatePopup(event, item) {
    this.showReinstate = true;
    if (!Array.isArray(item.calculation_fields)) item.calculation_fields = [item.calculation_fields];
    item.concept = item.concept != "" ? item.concept.split(',').map(item => item.trim()) : "";
    this.reInstateRuleVersionDetails = item;
    const parser = new DOMParser();
    const doc = parser.parseFromString(item.headerString, "text/html");
    const formattedDate = doc.querySelector("strong")?.textContent?.trim() || "";
    const formattedTime = doc.querySelector(".timepart")?.textContent?.trim() || "";
    this.ruleUpdatedDateTime = " Version " + item.version_seq + " from " + formattedDate + " at " + formattedTime;
  }

  /**
   * Triggered on click of reinstate button and navigate to edit rule with the selected rule version details
  */
  onReinstateAsCurrent(event) {
    this.reInstateRuleVersionDetails.button = 'Reinstate';
    this.buttonClicked.emit(this.reInstateRuleVersionDetails);
    this.showReinstate = false;
  }

  /**
   * Triggered on click of cancel reinstate button and navigate to edit rule current version 
  */
  cancelReinstate(event) {
    event.button = 'Cancel';
    this.buttonClicked.emit(event);
    this.showReinstate = false;
  }

  /**
   * modifies selected query builder criterias to the format query builder understands
  */
  modifyStructureToShowQB(qbQuery) {
    const operatorMap = operatorsMapToShowInQb;
    let customFields = [];
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case 'log':
          this.condition = v;
          break;
        case 'conditions':
          this.rules = v;
          break;
        case 'lval':
          this.field = v;
          break;
        case 'rval':
          this.value = v;
          this.startValue = v.start;
          this.endValue = v.end;
          customFields.push(v);
          break;
        case 'op':
          this.operator = operatorMap[v];
          break;
        case 'config':
        case 'operatorList':
        case 'delete':
        case 'json_path':
          delete qbQuery[k];
          break;
        default:
          return v;
      }
    });
    this.pushCustomFieldsToQBConfig(customFields);
    return parsed;
  }

  /**
   * Method pushes cusom fields to query Builder config
   */

  pushCustomFieldsToQBConfig(customFields): void {
    this.qbConfig.customFieldList = {};
    this.qbConfig.customFieldList.dataset = [];
    customFields.forEach(column => {
      this.qbConfig.customFieldList.dataset.push({ "name": column, "id": column, "collection": WORKSHEET_HEADERS });
    });
  }

  /**
   * Modify master data fields into the format query builder understands
  */
  modifyQBConfig(masterDataQBConfig) {
    let QBfields = {};
    let mutuallyExclusiveFields = { 'CLNT_ID': 'CNCPT_ID', 'CNCPT_ID': 'CLNT_ID', 'CNCPT_NM': 'CLNT_ID' };
    const typeMapping = {
      'decimal': 'numeric',
      'string': 'text',
      'date': 'calendar'
    };
    masterDataQBConfig.forEach(field => {
      switch (field.field_type) {
        case 'dropdown':
          QBfields[field.value] = { name: field.name, type: 'singleselect', dataset: field.options, key: 'name', id: 'id' };
          break;
        case 'freetext':
          QBfields[field.value] = { name: field.name, type: typeMapping[field.type] };
          if (field.type == 'date') {
            QBfields[field.value].dateFormat = 'YYYY-MM-DD';
          }
      }
      if (field.value == 'CLNT_ID' || field.value == 'CNCPT_ID' || field.value == 'CNCPT_NM') {
        QBfields[field.value].mutuallyExclusive = [mutuallyExclusiveFields[field.value]];
      }
    });
    return QBfields;
  }

}
