import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { EditComponent } from './edit.component';
import { RulesApiService } from '../_services/rules-api.service';
import { ToastService } from '../../_services/toast.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ProductApiService } from '../../_services/product-api.service';
import { UserManagementApiService } from '../../users/_services/user-management-api.service';
import { AuthService } from '../../_services/authentication.services';
import { CookieService } from 'ngx-cookie-service';

describe('EditComponent - 85%+ Coverage Test', () => {
  let component: EditComponent;
  let fixture: ComponentFixture<EditComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRulesApiService: jasmine.SpyObj<RulesApiService>;
  let mockToastService: jasmine.SpyObj<ToastService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate'], {
      url: '/rules/edit/123'
    });
    const rulesApiServiceSpy = jasmine.createSpyObj('RulesApiService', [
      'getAllViewEditRuleAPIs', 'getAssetsJson', 'createEditRule', 'deleteRule',
      'getColumnConfigJsonDuplicate', 'getFileDetailsOfRules', 'uploadFileAndQBCriteria',
      'addFilesToRules', 'getMultipleCriteriaFile', 'getInventoryStatusData'
    ]);
    const toastServiceSpy = jasmine.createSpyObj('ToastService', ['setSuccessNotification', 'setErrorNotification']);
    const utilitiesServiceSpy = jasmine.createSpyObj('UtilitiesService', ['getECPDateFormat']);
    const clientApiServiceSpy = jasmine.createSpyObj('ClientApiService', ['getAllClientsInPreferenceCenter']);
    const productApiServiceSpy = jasmine.createSpyObj('ProductApiService', ['getProductConceptsId']);
    const userManagementApiServiceSpy = jasmine.createSpyObj('UserManagementApiService', ['getAssetsJson']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getStoredUserProfile', 'piAuthorize']);
    const cookieServiceSpy = jasmine.createSpyObj('CookieService', ['get', 'set']);
    
    // Setup comprehensive service returns
    rulesApiServiceSpy.getInventoryStatusData.and.returnValue(of({
      result: [
        { cdValName: 'active', cdValLongDesc: 'Active Description' },
        { cdValName: 'inactive', cdValLongDesc: 'Inactive Description' }
      ]
    }));
    
    rulesApiServiceSpy.getAllViewEditRuleAPIs.and.returnValue(of([
      { 
        status: { code: 200 }, 
        result: { 
          fields: {
            rule_type: [{ name: 'Exclusion', id: 1 }],
            business_owners: [{ name: 'Owner 1', id: 1 }],
            rule_sub_type: [{ name: 'Subtype 1', id: 1 }]
          }
        } 
      },
      { 
        status: { code: 200 }, 
        result: { 
          metadata: { 
            rules: [{
              rule_id: 123,
              rule_name: 'Test Rule',
              rule_level: 'Client Level',
              created_by: 'test_user',
              version_seq: 1,
              is_draft: true,
              is_edited: false,
              rule_type: 'Exclusion',
              inventory_status: 'active',
              retro_apply: true,
              bypass_apply: false,
              header_level: true
            }]
          } 
        } 
      }
    ]));
    
    rulesApiServiceSpy.getFileDetailsOfRules.and.returnValue(of({
      status: { code: 200 },
      result: {
        files: [
          { file_name: 'test1.csv', file_size: 1024, upload_date: '2023-01-01' },
          { file_name: 'test2.csv', file_size: 2048, upload_date: '2023-01-02' }
        ]
      }
    }));
    
    rulesApiServiceSpy.createEditRule.and.returnValue(of({ status: { code: 200 } }));
    rulesApiServiceSpy.uploadFileAndQBCriteria.and.returnValue(of({
      result: { uploaded_files: [{ corpus_id: 'test_corpus_123' }] }
    }));
    rulesApiServiceSpy.getColumnConfigJsonDuplicate.and.returnValue(of({ result: {} }));

    await TestBed.configureTestingModule({
      declarations: [EditComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: { 
          snapshot: { 
            params: { id: '123' },
            paramMap: { get: () => '123' }
          } 
        }},
        { provide: RulesApiService, useValue: rulesApiServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: UtilitiesService, useValue: utilitiesServiceSpy },
        { provide: ClientApiService, useValue: clientApiServiceSpy },
        { provide: ProductApiService, useValue: productApiServiceSpy },
        { provide: UserManagementApiService, useValue: userManagementApiServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: CookieService, useValue: cookieServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EditComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRulesApiService = TestBed.inject(RulesApiService) as jasmine.SpyObj<RulesApiService>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;

    // Setup sessionStorage mock
    spyOn(sessionStorage, 'getItem').and.returnValue('1');
    
    fixture.detectChanges();
  });

  afterEach(() => {
    // Clean up to prevent afterAll errors
    if (component) {
      component.showLoader = false;
      component.inventoryStatusDataset = null;
      component.rule = null;
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component properties', () => {
    expect(component.ruleId).toBe(123);
    expect(component.headerText).toBe('Edit Rule 123');
    expect(component.breadcrumbDataset).toEqual([
      { label: 'Home', url: '/' },
      { label: 'Rules engine', url: '/rules' },
      { label: 'Edit rule' }
    ]);
  });

  it('should test constructor calls getInventoryStatusData', () => {
    expect(mockRulesApiService.getInventoryStatusData).toHaveBeenCalled();
  });

  it('should test ngOnInit comprehensive initialization', () => {
    spyOn(component, 'callGetRuleApis').and.stub();
    spyOn(component, 'getConfigForDuplicateRules').and.stub();
    
    component.ngOnInit();
    
    expect(component.ruleId).toBe(123);
    expect(component.headerText).toBe('Edit Rule 123');
    expect(component.callGetRuleApis).toHaveBeenCalled();
    expect(component.getConfigForDuplicateRules).toHaveBeenCalled();
  });

  it('should test validateEditDynamicForms comprehensive scenarios', () => {
    // Test submit with bypass and draft rule
    component.bypassApply = true;
    component.isDraftRule = true;
    component.mainFieldsNullCheckCount = 0;
    
    component.validateEditDynamicForms('submit');
    expect(component.openbypassConfirm).toBe(true);

    // Test save scenario
    component.bypassApply = false;
    component.validateEditDynamicForms('save');
    expect(component.mainFieldsNullCheckCount).toBe(0);
  });

  it('should test onSelect inventory status functionality', () => {
    const mockItem = {
      cdValName: 'active',
      cdValLongDesc: 'Active status description',
      cdValShrtDesc: 'Active'
    };
    
    component.onSelect(mockItem);
    
    expect(component.isEdited).toBe(true);
    expect(component.statusDescription).toBe('Active status description');
    expect(component.statusSuggestion).toBe('Active');
    expect(component.selectedValue).toBe('active');
    expect(component.searchResultsWindow).toBe(false);
    expect(component.suggestionWindow).toBe(false);
    expect(component.openAccordion).toBe(true);
  });

  it('should test validateEdit with different scenarios', () => {
    // Test Global Level scenario
    component.levelIndicator = 'Global Level';
    component.validateEdit();
    
    expect(component.showMessage).toBe(true);
    expect(component.displayDuplicateMessage).toBe(false);
    expect(component.displayStyle).toBe('block');

    // Test non-Global Level scenario
    component.levelIndicator = 'Client Level';
    spyOn(component, 'editRule').and.stub();
    
    component.validateEdit();
    expect(component.editRule).toHaveBeenCalled();
  });

  it('should test checkForDuplicateRules modal management', () => {
    component.checkForDuplicateRules();
    
    expect(component.editSubmitOpenModel).toBe(true);
    expect(component.showMessage).toBe(false);
    expect(component.displayDuplicateMessage).toBe(true);
    expect(component.displayStyle).toBe('block');
  });

  it('should test isDefined utility method comprehensively', () => {
    expect(component.isDefined('test')).toBe(true);
    expect(component.isDefined(0)).toBe(true);
    expect(component.isDefined(false)).toBe(true);
    expect(component.isDefined([])).toBe(true);
    expect(component.isDefined({})).toBe(true);
    expect(component.isDefined(null)).toBe(false);
    expect(component.isDefined(undefined)).toBe(false);
  });

  it('should test form event mapping methods', () => {
    // Test mapValuesFromGeneralToJson
    const generalEvent = {
      controls: { 
        rule_name: { value: 'Updated Rule' }, 
        description: { value: 'Updated Description' } 
      }
    };
    
    component.mapValuesFromGeneralToJson(generalEvent);
    
    expect(component.generalDetailsResponse).toEqual(generalEvent.controls);
    expect(component.generalDetailsFormEvent).toEqual(generalEvent);
    expect(component.isEdited).toBe(true);
  });

  it('should test file upload methods', () => {
    component.uploadFileInEditRule();
    expect(component.fileDetailsExcelOpenModel).toBe(true);
    expect(component.isFileReady).toBe(true);
    expect(component.fileUploadPopup).toBe('block');

    component.fileUploadpopUpReset();
    expect(component.isFileReady).toBe(false);
    expect(component.isTextReady).toBe(false);
    expect(component.fileUploadPopup).toBe('none');
  });

  it('should test form state methods', () => {
    const mockEvent = { toggle: true };
    component.setRetro(mockEvent);
    expect(component.rule['retro_apply']).toBe(true);
    expect(component.isEdited).toBe(true);
    
    component.setBypass({ toggle: false });
    expect(component.rule['bypass_apply']).toBe(false);

    component.setLevel({ toggle: true });
    expect(component.rule['header_level']).toBe(true);
  });

  it('should test API methods', () => {
    component.rule = {
      rule_name: 'Test Rule',
      rule_type: 'Exclusion',
      rule_subtype: 'Test Subtype',
      start_date: new Date(),
      end_date: new Date()
    };
    component.selectedValue = 'expiration';

    component.editRule();
    expect(mockRulesApiService.createEditRule).toHaveBeenCalled();

    component.getInventoryStatusData();
    expect(mockRulesApiService.getInventoryStatusData).toHaveBeenCalled();

    component.callGetFileDetailsRules('Global', 1);
    expect(mockRulesApiService.getFileDetailsOfRules).toHaveBeenCalledWith(123, 'Global', 1);
  });

  it('should test comprehensive file upload workflow', () => {
    // Test uploadMultiCriteriaFile disabled state
    component.isDisabled = true;
    const mockEvent = new Event('click');

    component.uploadMultiCriteriaFile(mockEvent);
    expect(mockRulesApiService.uploadFileAndQBCriteria).not.toHaveBeenCalled();

    // Test enabled state with successful upload
    component.isDisabled = false;
    component.fileUploadEditJSON = [new File(['test content'], 'test.csv')];
    component.postUploadDataJson = { commentsInUpload: 'Test upload' };
    component.corpusId = '';
    component.levelIndicator = 'Global Level';

    spyOn(component, 'removeFileParserTable').and.stub();

    component.uploadMultiCriteriaFile(mockEvent);

    expect(component.showSubmit).toBe(true);
    expect(component.corpusId).toBe('test_corpus_123');
    expect(component.showLoader).toBe(false);
    expect(component.uploadFileStatus).toBe('Success');
    expect(component.openFileUploadConfirmModal).toBe(true);
    expect(component.removeFileParserTable).toHaveBeenCalled();

    // Test upload error
    mockRulesApiService.uploadFileAndQBCriteria.and.returnValue(throwError({ statusText: 'Upload Failed' }));

    component.uploadMultiCriteriaFile(mockEvent);

    expect(component.showLoader).toBe(false);
    expect(component.uploadFileStatus).toBe('Fail');
    expect(component.uploadFileStatusMsg).toBe('Upload Failed');
    expect(component.openFileUploadConfirmModal).toBe(true);
  });

  it('should test callGetRuleApis comprehensive scenarios', () => {
    spyOn(component, 'refineMasterData').and.stub();
    spyOn(component, 'callGetFileDetailsRules').and.stub();
    component.isDataReceivedFromSubcription = false;

    component.callGetRuleApis();

    expect(component.showLoader).toBe(false);
    expect(component.ruleLevel).toBe('Client Level');
    expect(component.levelIndicator).toBe('Client Level');
    expect(component.createdBy).toBe('test_user');
    expect(component.isDraftRule).toBe(true);
    expect(component.refineMasterData).toHaveBeenCalled();
    expect(component.callGetFileDetailsRules).toHaveBeenCalledWith('Client Level', 1);
  });

  it('should test refineMasterData data transformation', () => {
    const mockMasterData = {
      rule_type: [{ name: 'Exclusion', id: 1 }],
      business_owners: [{ name: 'Owner 1', id: 1 }],
      rule_sub_type: [{ name: 'Subtype 1', id: 1 }]
    };

    const mockRuleInfo = {
      rule_id: 123,
      rule_name: 'Test Rule',
      rule_type: 'Exclusion',
      retro_apply: true,
      bypass_apply: false,
      header_level: true,
      inventory_status: 'active'
    };

    spyOn(component, 'populateAdditionalDetails').and.stub();

    component.refineMasterData(mockMasterData, mockRuleInfo);

    expect(component.masterDataFromAPI).toEqual(mockMasterData);
    expect(component.ruleTypes).toEqual(mockMasterData.rule_type);
    expect(component.businessOwners).toEqual(mockMasterData.business_owners);
    expect(component.retroApply).toBe(true);
    expect(component.bypassApply).toBe(false);
    expect(component.headerLevel).toBe(true);
    expect(component.selectedValue).toBe('active');
    expect(component.showForms).toBe(true);
    expect(component.showLoader).toBe(false);
  });

  it('should test showDescriptionandInventoryStatus method', () => {
    // Test with valid inventory status dataset
    component.inventoryStatusDataset = {
      result: [
        { cdValName: 'active', cdValLongDesc: 'Active Description' },
        { cdValName: 'inactive', cdValLongDesc: 'Inactive Description' }
      ]
    };
    component.selectedValue = 'active';

    component.showDescriptionandInventoryStatus();

    expect(component.statusDescription).toBe('Active Description');

    // Test with null dataset
    component.inventoryStatusDataset = null;
    expect(() => component.showDescriptionandInventoryStatus()).not.toThrow();

    // Test with undefined rule
    component.rule = null;
    expect(() => component.showDescriptionandInventoryStatus()).not.toThrow();
  });

  it('should test comprehensive error scenarios', () => {
    // Test editRule with API error
    mockRulesApiService.createEditRule.and.returnValue(throwError({
      status: { code: 500, traceback: 'Database Error' }
    }));

    component.editRule();

    expect(mockToastService.setErrorNotification).toHaveBeenCalled();
    expect(component.showLoader).toBe(false);

    // Test callGetRuleApis error handling
    const errorResponse = [
      { status: { code: 500, traceback: 'Server Error' } },
      { status: { code: 200 } }
    ];

    spyOn(console, 'log');
    mockRulesApiService.getAllViewEditRuleAPIs.and.returnValue(of(errorResponse));

    component.callGetRuleApis();

    expect(console.log).toHaveBeenCalledWith('Unsuccessful', 'Server Error');
    expect(component.showLoader).toBe(false);
  });

  it('should test additional form and modal methods', () => {
    // Test mapValuesFromMainToJson
    const mainEvent = {
      controls: {
        rule_type: { value: 'Exclusion' },
        rule_subtype: { value: 'Test Subtype' }
      }
    };

    component.mapValuesFromMainToJson(mainEvent);

    expect(component.mainDetailsResponse).toEqual(mainEvent.controls);
    expect(component.mainDetailsFormEvent).toEqual(mainEvent);
    expect(component.isEdited).toBe(true);

    // Test mapValuesFromAdditionalToJson
    const additionalEvent = {
      controls: {
        external_point_of_contact: { value: '<EMAIL>' }
      }
    };

    component.mapValuesFromAdditionalToJson(additionalEvent);

    expect(component.additionalDetailsResponse).toEqual(additionalEvent.controls);
    expect(component.additionalDetailsFormEvent).toEqual(additionalEvent);
    expect(component.isEdited).toBe(true);

    // Test closePopup
    component.displayStyle = 'block';
    component.closePopup();
    expect(component.displayStyle).toBe('none');

    // Test closePopupUploadForEditRule
    component.closePopupUploadForEditRule();
    expect(component.fileUploadPopup).toBe('none');

    // Test onSubmitSkipClickedEitRule
    component.onSubmitSkipClickedEitRule();
    expect(component.fileUploadPopup).toBe('none');
  });

  it('should test file validation methods', () => {
    // Test validateMaxFileSize with small file
    component.fileUploadEditJSON = [new File(['small content'], 'small.csv', { type: 'text/csv' })];
    const isMaxedOut = component.validateMaxFileSize();
    expect(typeof isMaxedOut).toBe('boolean');

    // Test checkValidationForUploadFile
    component.postUploadDataJson = { commentsInUpload: 'Test comment' };
    component.showMaxLimitMsg = false;
    component.checkValidationForUploadFile();
    expect(component.isDisabled).toBe(false);

    // Test upload method with file
    const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const mockEvent = { target: { files: [mockFile] } } as any;

    spyOn(component, 'validateMaxFileSize').and.returnValue(false);
    spyOn(component, 'checkValidationForUploadFile').and.stub();

    component.upload(mockEvent);
    expect(component.fileUploadEditJSON).toEqual(mockEvent);
    expect(component.showMaxLimitMsg).toBe(false);
    expect(component.checkValidationForUploadFile).toHaveBeenCalled();
  });

  it('should test comprehensive edge cases and property management', () => {
    // Test various component state changes
    component.conceptsChangedPopUp = false;
    component.openFileUploadConfirmModal = false;
    component.editSubmitOpenModel = false;
    component.searchResultsWindow = true;
    component.suggestionWindow = true;
    component.openAccordion = false;

    // Test state changes through onSelect
    const mockItem = { cdValName: 'test', cdValShrtDesc: 'Test' };
    component.onSelect(mockItem);

    expect(component.searchResultsWindow).toBe(false);
    expect(component.suggestionWindow).toBe(false);
    expect(component.openAccordion).toBe(true);
    expect(component.isEdited).toBe(true);

    // Test component properties initialization
    expect(component.showMaxLimitMsg).toBe(false);
    expect(component.enableInventoryStatus).toBe(true);
    expect(component.noResultsFound).toBe(false);
    expect(component.isConceptDataReady).toBe(true);
    expect(component.conceptsChangedPopUp).toBe(false);
  });

  it('should test populateAdditionalDetails method', () => {
    const mockUpdateData = {
      files: [
        {
          file_name: 'test.csv',
          file_size: 1024,
          upload_date: '2023-01-01',
          comments: 'Test file'
        }
      ]
    };

    component.rule = { external_point_of_contact: '<EMAIL>' };
    component.populateAdditionalDetails(mockUpdateData);

    expect(component.additionalDetailsJson).toBeDefined();
    expect(component.additionalDetailsJson[0].groupControls[0].value).toBe('<EMAIL>');
    expect(component.additionalDetailsJson[0].groupControls[0].id).toBe('external_point_of_contact');
  });

  it('should test getConfigForDuplicateRules method', () => {
    component.getConfigForDuplicateRules();
    expect(mockRulesApiService.getColumnConfigJsonDuplicate).toHaveBeenCalled();
  });
});
